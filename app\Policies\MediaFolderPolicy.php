<?php

namespace App\Policies;

use App\Models\MediaFolder;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class MediaFolderPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can_upload_media ||
               $user->user_type->canManageCompanyMedia() ||
               $user->user_type->canAccessAllMedia();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, MediaFolder $mediaFolder): bool
    {
        return $mediaFolder->canBeAccessedBy($user);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can_upload_media ||
               $user->user_type->canManageCompanyMedia() ||
               $user->user_type->canAccessAllMedia();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, MediaFolder $mediaFolder): bool
    {
        // Super admin can update any folder
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        // Company admin can update company folders
        if ($user->user_type->canManageCompanyMedia()) {
            return $mediaFolder->company_id === $user->currentTeam?->id;
        }

        // Users can only update their own folders
        return $mediaFolder->created_by === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, MediaFolder $mediaFolder): bool
    {
        return $this->update($user, $mediaFolder);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, MediaFolder $mediaFolder): bool
    {
        return $this->delete($user, $mediaFolder);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, MediaFolder $mediaFolder): bool
    {
        return $user->user_type->canAccessAllMedia();
    }
}
