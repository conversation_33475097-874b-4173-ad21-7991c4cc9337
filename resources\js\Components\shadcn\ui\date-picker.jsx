import * as React from "react"
import { format, parse, isValid } from "date-fns"
import { Calendar as CalendarIcon, Clock } from "lucide-react"

import { cn } from "@/Components/lib/utils"
import { Button } from "@/Components/shadcn/ui/button"
import { Calendar } from "@/Components/shadcn/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/Components/shadcn/ui/popover"
import { Input } from "@/Components/shadcn/ui/input"
import { Label } from "@/Components/shadcn/ui/label"

const DatePicker = React.forwardRef(({
  date,
  onSelect,
  disabled,
  placeholder = "Pick a date",
  className,
  includeTime = false,
  mode = "single",
  numberOfMonths = 1,
  ...props
}, ref) => {
  const [selectedDate, setSelectedDate] = React.useState(mode === "single" ? date : undefined)
  const [selectedRange, setSelectedRange] = React.useState(mode === "range" ? date : undefined)
  const [time, setTime] = React.useState("00:00")

  React.useEffect(() => {
    if (mode === "single") {
      setSelectedDate(date)
    } else {
      setSelectedRange(date)
    }
  }, [date, mode])

  const handleDateSelect = (newDate) => {
    setSelectedDate(newDate)
    if (includeTime && newDate) {
      const [hours, minutes] = time.split(':')
      const dateWithTime = new Date(newDate)
      dateWithTime.setHours(parseInt(hours), parseInt(minutes))
      onSelect?.(dateWithTime)
    } else {
      onSelect?.(newDate)
    }
  }

  const handleRangeSelect = (range) => {
    setSelectedRange(range)
    onSelect?.(range)
  }

  const handleTimeChange = (e) => {
    const newTime = e.target.value
    setTime(newTime)

    if (selectedDate) {
      const [hours, minutes] = newTime.split(':')
      const dateWithTime = new Date(selectedDate)
      dateWithTime.setHours(parseInt(hours), parseInt(minutes))
      onSelect?.(dateWithTime)
    }
  }

  const formatDisplayDate = (date) => {
    if (!date) return ""
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...(includeTime && {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    })
  }

  const formatDisplayRange = (range) => {
    if (!range?.from) return ""
    const from = formatDisplayDate(range.from)
    const to = range.to ? formatDisplayDate(range.to) : ""
    return to ? `${from} - ${to}` : from
  }

  const formatDisplayValue = () => {
    if (mode === "single") {
      return selectedDate ? formatDisplayDate(selectedDate) : placeholder
    } else {
      return selectedRange ? formatDisplayRange(selectedRange) : placeholder
    }
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            ref={ref}
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !((mode === "single" && selectedDate) || (mode === "range" && selectedRange?.from)) && "text-muted-foreground"
            )}
            disabled={disabled}
            {...props}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formatDisplayValue()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            {mode === "single" ? (
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={disabled}
                numberOfMonths={numberOfMonths}
                captionLayout="dropdown"
                className="rounded-md border-0"
              />
            ) : (
              <Calendar
                mode="range"
                selected={selectedRange}
                onSelect={handleRangeSelect}
                disabled={disabled}
                numberOfMonths={numberOfMonths}
                captionLayout="dropdown"
                className="rounded-md border-0"
              />
            )}
            {includeTime && (
              <div className="mt-3 flex items-center gap-2 border-t pt-3">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <Input
                  type="time"
                  value={time}
                  onChange={handleTimeChange}
                  disabled={disabled}
                  className="h-8"
                  step="60"
                />
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
})
DatePicker.displayName = "DatePicker"

export { DatePicker }
