<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\WelcomeController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\User\OauthController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\User\LoginLinkController;

Route::get('/', [WelcomeController::class, 'home'])->name('home');

Route::prefix('auth')->group(
    function () {
        // OAuth
        Route::get('/redirect/{provider}', [OauthController::class, 'redirect'])->name('oauth.redirect');
        Route::get('/callback/{provider}', [OauthController::class, 'callback'])->name('oauth.callback');
        // Magic Link
        Route::middleware('throttle:login-link')->group(function () {
            Route::post('/login-link', [LoginLinkController::class, 'store'])->name('login-link.store');
            Route::get('/login-link/{token}', [LoginLinkController::class, 'login'])
                ->name('login-link.login')
                ->middleware('signed');
        });
    }
);

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified'])->group(function () {
    Route::get('/dashboard', DashboardController::class)->name('dashboard');

    Route::delete('/auth/destroy/{provider}', [OauthController::class, 'destroy'])->name('oauth.destroy');

    Route::get('/chat', [ChatController::class, 'index'])->name('chat.index');

    Route::resource('/subscriptions', SubscriptionController::class)
        ->names('subscriptions')
        ->only(['index', 'create', 'store', 'show']);

    // Admin routes (for super admin users)
    Route::middleware(['can:access-admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        Route::resource('plans', \App\Http\Controllers\Admin\PlanController::class);

        // API routes for DataTables
        Route::get('users/api/data', [\App\Http\Controllers\Admin\UserController::class, 'apiIndex'])->name('users.api');
        Route::get('plans/api/data', [\App\Http\Controllers\Admin\PlanController::class, 'apiIndex'])->name('plans.api');

        // Media Management routes
        Route::resource('media', \App\Http\Controllers\Admin\MediaController::class);
        Route::resource('media-folders', \App\Http\Controllers\Admin\MediaFolderController::class);

        // Media API routes
        Route::post('media/bulk-destroy', [\App\Http\Controllers\Admin\MediaController::class, 'bulkDestroy'])->name('media.bulk-destroy');
        Route::post('media/move-to-folder', [\App\Http\Controllers\Admin\MediaController::class, 'moveToFolder'])->name('media.move-to-folder');
    });

    // Company routes (for company admin users)
    Route::middleware(['auth:sanctum', 'verified'])->prefix('company')->name('company.')->group(function () {
        Route::resource('agents', \App\Http\Controllers\Company\AgentController::class);

        // Media Management routes for company
        Route::resource('media', \App\Http\Controllers\Admin\MediaController::class);
        Route::resource('media-folders', \App\Http\Controllers\Admin\MediaFolderController::class);

        // Media API routes for company
        Route::post('media/bulk-destroy', [\App\Http\Controllers\Admin\MediaController::class, 'bulkDestroy'])->name('media.bulk-destroy');
        Route::post('media/move-to-folder', [\App\Http\Controllers\Admin\MediaController::class, 'moveToFolder'])->name('media.move-to-folder');

        // Debug route to check user permissions
        Route::get('debug-user', function(\Illuminate\Http\Request $request) {
            $user = $request->user();
            return response()->json([
                'user_id' => $user->id,
                'user_type' => $user->user_type->value,
                'current_team_id' => $user->currentTeam?->id,
                'can_upload_media' => $user->can_upload_media ?? 'null',
                'canAccessAllMedia' => $user->user_type->canAccessAllMedia(),
                'canManageCompanyMedia' => $user->user_type->canManageCompanyMedia(),
                'user_attributes' => $user->toArray(),
            ]);
        });

        // Test upload route without policy
        Route::post('test-upload', function(\Illuminate\Http\Request $request) {
            \Log::info('Test upload route hit', [
                'user_id' => $request->user()->id,
                'user_type' => $request->user()->user_type->value,
                'files_count' => $request->hasFile('files') ? count($request->file('files')) : 0,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test upload route working',
                'user_type' => $request->user()->user_type->value,
            ]);
        });
    });
});
