<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('media_folders', function (Blueprint $table) {
            // Add composite index for faster uniqueness checks
            $table->index(['name', 'parent_id', 'company_id'], 'media_folders_name_parent_company_index');
            $table->index(['slug', 'parent_id', 'company_id'], 'media_folders_slug_parent_company_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('media_folders', function (Blueprint $table) {
            $table->dropIndex('media_folders_name_parent_company_index');
            $table->dropIndex('media_folders_slug_parent_company_index');
        });
    }
};
