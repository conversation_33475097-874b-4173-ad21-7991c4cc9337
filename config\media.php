<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Media Storage Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the storage settings for media files including
    | supported file types, upload limits, and thumbnail configurations.
    |
    */

    'disk' => env('MEDIA_DISK', 'public'),

    'path' => env('MEDIA_PATH', 'media'),

    /*
    |--------------------------------------------------------------------------
    | File Upload Limits
    |--------------------------------------------------------------------------
    |
    | Configure the maximum file size and other upload restrictions.
    |
    */

    'max_file_size' => env('MEDIA_MAX_FILE_SIZE', 52428800), // 50MB in bytes

    'max_files_per_upload' => env('MEDIA_MAX_FILES_PER_UPLOAD', 100),

    /*
    |--------------------------------------------------------------------------
    | Supported File Types
    |--------------------------------------------------------------------------
    |
    | Define which file types are allowed for upload, organized by category.
    |
    */

    'supported_types' => [
        'image' => [
            'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'],
            'mime_types' => [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/svg+xml',
                'image/bmp',
            ],
        ],
        'video' => [
            'extensions' => ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv', 'mkv'],
            'mime_types' => [
                'video/mp4',
                'video/webm',
                'video/ogg',
                'video/quicktime',
                'video/x-msvideo',
                'video/x-ms-wmv',
                'video/x-flv',
                'video/x-matroska',
            ],
        ],
        'audio' => [
            'extensions' => ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma'],
            'mime_types' => [
                'audio/mpeg',
                'audio/wav',
                'audio/ogg',
                'audio/aac',
                'audio/flac',
                'audio/mp4',
                'audio/x-ms-wma',
            ],
        ],
        'document' => [
            'extensions' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'csv'],
            'mime_types' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
                'application/rtf',
                'text/csv',
            ],
        ],
        'archive' => [
            'extensions' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'mime_types' => [
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed',
                'application/x-tar',
                'application/gzip',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Thumbnail Configuration
    |--------------------------------------------------------------------------
    |
    | Configure thumbnail generation settings including sizes and quality.
    | Thumbnails are stored in the same folder as original files (WordPress style).
    |
    */

    'thumbnails' => [
        'enabled' => env('MEDIA_THUMBNAILS_ENABLED', true),

        'disk' => env('MEDIA_THUMBNAILS_DISK', 'public'),

        // Same folder as original files (WordPress style)
        'same_folder' => true,

        'quality' => env('MEDIA_THUMBNAIL_QUALITY', 85),

        'sizes' => [
            'thumb' => [
                'width' => 150,
                'height' => 150,
                'crop' => true,
                'description' => 'Small thumbnail for grid view',
            ],
            'small' => [
                'width' => 300,
                'height' => 300,
                'crop' => false,
                'description' => 'Small size for previews',
            ],
            'medium' => [
                'width' => 600,
                'height' => 600,
                'crop' => false,
                'description' => 'Medium size for modal previews',
            ],
            'large' => [
                'width' => 1200,
                'height' => 1200,
                'crop' => false,
                'description' => 'Large size for detailed view',
            ],
        ],

        'default_size' => 'thumb',

        'fallback_sizes' => ['small', 'medium', 'original'],

        // WordPress style naming: image-150x150.jpg, image-300x300.jpg
        'naming_pattern' => '{filename}-{width}x{height}.{extension}',
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing
    |--------------------------------------------------------------------------
    |
    | Configure image processing options and optimization settings.
    |
    */

    'image_processing' => [
        'driver' => env('MEDIA_IMAGE_DRIVER', 'gd'), // gd or imagick

        'auto_orient' => true,

        'strip_metadata' => false,

        'progressive_jpeg' => true,

        'optimize' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Metadata Extraction
    |--------------------------------------------------------------------------
    |
    | Configure which metadata should be extracted from uploaded files.
    |
    */

    'metadata' => [
        'extract_exif' => true,
        'extract_iptc' => true,
        'extract_gps' => true,
        'extract_dimensions' => true,
        'extract_duration' => true, // for video/audio files
        'extract_color_palette' => false, // requires additional processing
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Configure security-related settings for media uploads.
    |
    */

    'security' => [
        'scan_uploads' => env('MEDIA_SCAN_UPLOADS', false),
        'allowed_domains' => [], // Empty array allows all domains
        'blocked_extensions' => ['exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js'],
        'validate_image_contents' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Configure performance-related settings for media processing.
    |
    */

    'performance' => [
        'queue_thumbnail_generation' => env('MEDIA_QUEUE_THUMBNAILS', false),
        'queue_metadata_extraction' => env('MEDIA_QUEUE_METADATA', false),
        'lazy_load_thumbnails' => true,
        'cache_thumbnails' => true,
        'cache_duration' => 86400, // 24 hours in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | URL Generation
    |--------------------------------------------------------------------------
    |
    | Configure how URLs are generated for media files and thumbnails.
    |
    */

    'urls' => [
        'signed' => env('MEDIA_SIGNED_URLS', false),
        'expires_in' => 3600, // 1 hour for signed URLs
        'cdn_url' => env('MEDIA_CDN_URL', null),
    ],

];
