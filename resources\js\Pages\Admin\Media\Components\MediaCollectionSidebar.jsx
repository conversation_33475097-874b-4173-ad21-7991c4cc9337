import React from 'react';
import { router } from '@inertiajs/react';

import { Button } from '@/components/shadcn/ui/button';
import { ScrollArea } from '@/components/shadcn/ui/scroll-area';
import { Separator } from '@/components/shadcn/ui/separator';
import { Badge } from '@/components/shadcn/ui/badge';
import {
    FolderPlus,
    Folder,
    FolderOpen,
    Building,
    User,
    Image,
    Video,
    Music,
    FileText,
    File
} from 'lucide-react';

export default function MediaCollectionSidebar({
    collections,
    onCreateCollection,
    userPermissions
}) {
    // Determine the correct route based on current URL
    const isCompanyContext = window.location.pathname.includes('/company/');
    const mediaIndexRoute = isCompanyContext ? 'company.media.index' : 'admin.media.index';

    const handleNavigateToCollection = (collectionId) => {
        router.get(route(mediaIndexRoute), { collection_id: collectionId });
    };

    const handleNavigateToAll = () => {
        router.get(route(mediaIndexRoute));
    };

    const handleFilterByType = (type) => {
        router.get(route(mediaIndexRoute), { type });
    };

    return (
        <div className="h-full flex flex-col">
            {/* Header */}
            <div className="p-4 border-b">
                <div className="flex items-center justify-between mb-3">
                    <h2 className="font-semibold">Media Library</h2>
                    {userPermissions.canUpload && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onCreateCollection}
                        >
                            <FolderPlus className="h-4 w-4" />
                        </Button>
                    )}
                </div>

                {/* Quick Filters */}
                <div className="space-y-1">
                    <Button
                        variant="ghost"
                        className="w-full justify-start h-8 px-2"
                        onClick={handleNavigateToAll}
                    >
                        <Folder className="h-4 w-4 mr-2" />
                        All Media
                    </Button>
                </div>
            </div>

            <ScrollArea className="flex-1">
                <div className="p-4 space-y-6">
                    {/* File Type Filters */}
                    <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">
                            File Types
                        </h3>
                        <div className="space-y-1">
                            <Button
                                variant="ghost"
                                className="w-full justify-start h-8 px-2"
                                onClick={() => handleFilterByType('image')}
                            >
                                <Image className="h-4 w-4 mr-2 text-blue-500" />
                                Images
                            </Button>
                            <Button
                                variant="ghost"
                                className="w-full justify-start h-8 px-2"
                                onClick={() => handleFilterByType('video')}
                            >
                                <Video className="h-4 w-4 mr-2 text-purple-500" />
                                Videos
                            </Button>
                            <Button
                                variant="ghost"
                                className="w-full justify-start h-8 px-2"
                                onClick={() => handleFilterByType('audio')}
                            >
                                <Music className="h-4 w-4 mr-2 text-green-500" />
                                Audio
                            </Button>
                            <Button
                                variant="ghost"
                                className="w-full justify-start h-8 px-2"
                                onClick={() => handleFilterByType('document')}
                            >
                                <FileText className="h-4 w-4 mr-2 text-orange-500" />
                                Documents
                            </Button>
                        </div>
                    </div>

                    {/* Collections */}
                    {collections && collections.length > 0 && (
                        <>
                            <Separator />
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-2">
                                    Collections
                                </h3>
                                <div className="space-y-1">
                                    {collections.map((collection) => (
                                        <Button
                                            key={collection.id}
                                            variant="ghost"
                                            className="w-full justify-start h-auto p-2"
                                            onClick={() => handleNavigateToCollection(collection.id)}
                                        >
                                            <div className="flex items-center gap-2 w-full">
                                                <div className="flex-shrink-0">
                                                    {collection.company_id ? (
                                                        <Building className="h-4 w-4 text-purple-500" />
                                                    ) : (
                                                        <User className="h-4 w-4 text-gray-500" />
                                                    )}
                                                </div>
                                                <div className="flex-1 text-left min-w-0">
                                                    <p className="text-sm font-medium truncate">
                                                        {collection.name}
                                                    </p>
                                                    {collection.description && (
                                                        <p className="text-xs text-muted-foreground truncate">
                                                            {collection.description}
                                                        </p>
                                                    )}
                                                </div>
                                                {collection.media_count > 0 && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        {collection.media_count}
                                                    </Badge>
                                                )}
                                            </div>
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </>
                    )}

                    {/* Access Level Info */}
                    <Separator />
                    <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">
                            Access Level
                        </h3>
                        <div className="space-y-2 text-xs text-muted-foreground">
                            {userPermissions.canAccessAllMedia && (
                                <div className="flex items-center gap-2">
                                    <Globe className="h-3 w-3" />
                                    <span>Full Access (Super Admin)</span>
                                </div>
                            )}
                            {userPermissions.canManageCompanyMedia && !userPermissions.canAccessAllMedia && (
                                <div className="flex items-center gap-2">
                                    <Building className="h-3 w-3" />
                                    <span>Company Media Manager</span>
                                </div>
                            )}
                            {!userPermissions.canManageCompanyMedia && !userPermissions.canAccessAllMedia && (
                                <div className="flex items-center gap-2">
                                    <User className="h-3 w-3" />
                                    <span>Personal Media Only</span>
                                </div>
                            )}
                            {userPermissions.canUpload && (
                                <div className="flex items-center gap-2">
                                    <File className="h-3 w-3" />
                                    <span>Can Upload Files</span>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </ScrollArea>
        </div>
    );
}
