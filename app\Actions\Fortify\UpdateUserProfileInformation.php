<?php

declare(strict_types=1);

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Validation\Rule;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

final class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     *
     * @param  array<string, mixed>  $input
     */
    public function update(User $user, array $input): void
    {
        // Base validation rules
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'photo' => ['nullable', 'mimes:jpg,jpeg,png', 'max:1024'],
            'profile_image_id' => ['nullable', 'exists:media,id'],
        ];

        // Add user type specific validation rules
        if ($user->user_type === 'company_admin') {
            $rules['company_name'] = ['nullable', 'string', 'max:255'];
        }

        if ($user->user_type === 'individual_agent') {
            $rules['license_number'] = ['nullable', 'string', 'max:50'];
            $rules['license_state'] = ['nullable', 'string', 'max:2'];
            $rules['license_expiry'] = ['nullable', 'date'];
        }

        Validator::make($input, $rules)->validateWithBag('updateProfileInformation');

        if (isset($input['photo']) && $input['photo'] instanceof UploadedFile) {
            $user->updateProfilePhoto($input['photo']);
        }

        // Prepare data for update
        $updateData = [
            'name' => $input['name'],
            'email' => $input['email'],
            'phone' => $input['phone'] ?? null,
            'profile_image_id' => $input['profile_image_id'] ?? null,
        ];

        // Add user type specific fields
        if ($user->user_type === 'company_admin') {
            $updateData['company_name'] = $input['company_name'] ?? null;
        }

        if ($user->user_type === 'individual_agent') {
            $updateData['license_number'] = $input['license_number'] ?? null;
            $updateData['license_state'] = $input['license_state'] ?? null;
            $updateData['license_expiry'] = $input['license_expiry'] ?? null;
        }

        if ($input['email'] !== $user->email && $user->hasVerifiedEmail()) {
            $this->updateVerifiedUser($user, $updateData);
        } else {
            $user->forceFill($updateData)->save();
        }
    }

    /**
     * Update the given verified user's profile information.
     *
     * @param  array<string, mixed>  $input
     */
    private function updateVerifiedUser(User $user, array $input): void
    {
        $input['email_verified_at'] = null;
        $user->forceFill($input)->save();
        $user->sendEmailVerificationNotification();
    }
}
