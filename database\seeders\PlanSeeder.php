<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Starter Plan',
                'description' => 'Perfect for individual agents getting started in real estate',
                'price' => 29.99,
                'billing_period' => 'monthly',
                'stripe_price_id' => null,
                'features' => [
                    'Up to 10 active listings',
                    'Basic CRM features',
                    'Email support',
                    'Mobile app access',
                    'Basic reporting',
                ],
                'is_active' => true,
                'is_popular' => false,
                'max_agents' => 1,
                'max_listings' => 10,
                'sort_order' => 1,
            ],
            [
                'name' => 'Professional Plan',
                'description' => 'Ideal for growing agents and small teams',
                'price' => 79.99,
                'billing_period' => 'monthly',
                'stripe_price_id' => null,
                'features' => [
                    'Up to 50 active listings',
                    'Advanced CRM features',
                    'Priority email support',
                    'Mobile app access',
                    'Advanced reporting & analytics',
                    'Lead management tools',
                    'Custom branding',
                ],
                'is_active' => true,
                'is_popular' => true,
                'max_agents' => 5,
                'max_listings' => 50,
                'sort_order' => 2,
            ],
            [
                'name' => 'Enterprise Plan',
                'description' => 'For large agencies and brokerages',
                'price' => 199.99,
                'billing_period' => 'monthly',
                'stripe_price_id' => null,
                'features' => [
                    'Unlimited listings',
                    'Full CRM suite',
                    'Phone & email support',
                    'Mobile app access',
                    'Advanced reporting & analytics',
                    'Lead management tools',
                    'Custom branding',
                    'API access',
                    'White-label options',
                    'Dedicated account manager',
                ],
                'is_active' => true,
                'is_popular' => false,
                'max_agents' => null,
                'max_listings' => null,
                'sort_order' => 3,
            ],
            [
                'name' => 'Starter Annual',
                'description' => 'Perfect for individual agents - Annual billing (2 months free)',
                'price' => 299.99,
                'billing_period' => 'yearly',
                'stripe_price_id' => null,
                'features' => [
                    'Up to 10 active listings',
                    'Basic CRM features',
                    'Email support',
                    'Mobile app access',
                    'Basic reporting',
                    '2 months free with annual billing',
                ],
                'is_active' => true,
                'is_popular' => false,
                'max_agents' => 1,
                'max_listings' => 10,
                'sort_order' => 4,
            ],
        ];

        foreach ($plans as $planData) {
            Plan::create($planData);
        }
    }
}
