import React, { useState } from 'react';
import { Button } from '@/Components/shadcn/ui/button';
import { Label } from '@/Components/shadcn/ui/label';
import { Badge } from '@/Components/shadcn/ui/badge';
import { Card, CardContent } from '@/Components/shadcn/ui/card';
import {
    ImageIcon,
    Upload,
    X,
    FileIcon,
    VideoIcon,
    FileTextIcon,
    MusicIcon
} from 'lucide-react';
import MediaPickerModal from './MediaPickerModal';

/**
 * Reusable MediaSelector component for selecting media files
 *
 * @param {Object} props
 * @param {string} props.label - Label for the field
 * @param {number|null} props.value - Selected media ID
 * @param {function} props.onChange - Callback when media is selected/changed
 * @param {Object|null} props.selectedMedia - Currently selected media object
 * @param {string} props.accept - File types to accept (e.g., 'image/*', 'video/*', 'all')
 * @param {boolean} props.required - Whether the field is required
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.description - Help text
 * @param {string} props.error - Error message
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the field is disabled
 */
export default function MediaImageSelector({
    label,
    value,
    onChange,
    selectedMedia = null,
    accept = 'all',
    required = false,
    placeholder = 'Click to select media',
    description = null,
    error = null,
    className = '',
    disabled = false
}) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentMedia, setCurrentMedia] = useState(selectedMedia);

    // Update currentMedia when selectedMedia prop changes
    React.useEffect(() => {
        setCurrentMedia(selectedMedia);
    }, [selectedMedia]);

    const handleMediaSelect = (media) => {
        console.log('MediaImageSelector - handleMediaSelect called with:', media);
        setCurrentMedia(media); // Immediate preview update
        onChange(media.id, media);
        setIsModalOpen(false);
    };

    const handleClear = () => {
        console.log('MediaImageSelector - handleClear called');
        setCurrentMedia(null); // Clear preview immediately
        onChange(null, null);
    };

    const getFileIcon = (media) => {
        if (!media) return <ImageIcon className="h-8 w-8 text-muted-foreground" />;

        if (media.file_type === 'image') {
            return <ImageIcon className="h-8 w-8 text-blue-500" />;
        } else if (media.file_type === 'video') {
            return <VideoIcon className="h-8 w-8 text-purple-500" />;
        } else if (media.file_type === 'audio') {
            return <MusicIcon className="h-8 w-8 text-green-500" />;
        } else if (media.file_type === 'document') {
            return <FileTextIcon className="h-8 w-8 text-orange-500" />;
        } else {
            return <FileIcon className="h-8 w-8 text-gray-500" />;
        }
    };

    const getAcceptFilter = () => {
        switch (accept) {
            case 'image/*':
                return { type: 'image' };
            case 'video/*':
                return { type: 'video' };
            case 'audio/*':
                return { type: 'audio' };
            case 'document/*':
                return { type: 'document' };
            default:
                return {};
        }
    };

    return (
        <div className={`space-y-2 ${className}`}>
            {label && (
                <Label className="text-sm font-medium">
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </Label>
            )}

            {/* Selected Media Display - Square Card Design */}
            {currentMedia ? (
                <div className="space-y-3">
                    {/* Square Preview Card */}
                    <Card className="w-[150px] h-[150px] border-2 border-primary/20 overflow-hidden">
                        <CardContent className="p-0 h-full relative group">
                            {/* Image Preview */}
                            {currentMedia.thumbnail_url || currentMedia.url ? (
                                <img
                                    src={currentMedia.thumbnail_url || currentMedia.url}
                                    alt={currentMedia.alt_text || currentMedia.original_filename}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                        console.log('Image failed to load:', e.target.src);
                                        e.target.style.display = 'none';
                                        e.target.nextSibling.style.display = 'flex';
                                    }}
                                />
                            ) : null}

                            {/* Fallback Icon */}
                            <div className="w-full h-full bg-muted flex items-center justify-center" style={{display: currentMedia.thumbnail_url || currentMedia.url ? 'none' : 'flex'}}>
                                {getFileIcon(currentMedia)}
                            </div>

                            {/* Overlay with actions */}
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    size="sm"
                                    onClick={() => setIsModalOpen(true)}
                                    disabled={disabled}
                                >
                                    Change
                                </Button>
                                <Button
                                    type="button"
                                    variant="destructive"
                                    size="sm"
                                    onClick={handleClear}
                                    disabled={disabled}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Media Info */}
                    <div className="max-w-[150px]">
                        <p className="text-sm font-medium text-foreground truncate">
                            {currentMedia.title || currentMedia.original_filename}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                                {currentMedia.file_type || 'unknown'}
                            </Badge>
                            {currentMedia.formatted_size && (
                                <span className="text-xs text-muted-foreground">
                                    {currentMedia.formatted_size}
                                </span>
                            )}
                        </div>
                        {currentMedia.alt_text && (
                            <p className="text-xs text-muted-foreground mt-1 truncate">
                                {currentMedia.alt_text}
                            </p>
                        )}
                    </div>
                </div>
            ) : (
                /* Empty State - Square Card */
                <Card
                    className={`w-[150px] h-[150px] border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => !disabled && setIsModalOpen(true)}
                >
                    <CardContent className="p-4 h-full flex flex-col items-center justify-center text-center">
                        <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                        <p className="text-xs font-medium text-foreground mb-1">
                            {placeholder}
                        </p>
                        <p className="text-xs text-muted-foreground">
                            Click to select
                        </p>
                    </CardContent>
                </Card>
            )}

            {/* Description */}
            {description && (
                <p className="text-xs text-muted-foreground">{description}</p>
            )}

            {/* Error */}
            {error && (
                <p className="text-xs text-red-500">{error}</p>
            )}

            {/* Media Picker Modal */}
            <MediaPickerModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSelect={handleMediaSelect}
                filters={getAcceptFilter()}
                title={`Select ${accept === 'all' ? 'Media' : accept.replace('/*', '').charAt(0).toUpperCase() + accept.replace('/*', '').slice(1)}`}
            />
        </div>
    );
}
