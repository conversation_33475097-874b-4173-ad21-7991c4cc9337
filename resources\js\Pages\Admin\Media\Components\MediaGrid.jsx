import React, { useState } from 'react';
import { router } from '@inertiajs/react';
import { format } from 'date-fns';

import { Card, CardContent, CardFooter } from '@/components/shadcn/ui/card';
import { Checkbox } from '@/components/shadcn/ui/checkbox';
import { Badge } from '@/components/shadcn/ui/badge';
import { Button } from '@/components/shadcn/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
    DropdownMenuItem,
    DropdownMenuSeparator
} from '@/components/shadcn/ui/dropdown-menu';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/components/shadcn/ui/tooltip';
import {
    FileImage,
    FileText,
    FileVideo,
    FileAudio,
    File,
    MoreHorizontal,
    Eye,
    Edit,
    Trash2,
    Download,
    Copy
} from 'lucide-react';

import MediaDetailsModal from './MediaDetailsModal';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/shadcn/ui/pagination';

export default function MediaGrid({
    media,
    selectedMedia,
    onMediaSelect,
    onSelectAll,
    userPermissions
}) {
    const [selectedMediaItem, setSelectedMediaItem] = useState(null);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    // Get file icon based on type
    const getFileIcon = (mediaItem) => {
        const iconClass = "h-8 w-8 text-muted-foreground";

        if (mediaItem.file_type === 'image') {
            return <FileImage className={iconClass} />;
        } else if (mediaItem.file_type === 'video') {
            return <FileVideo className={iconClass} />;
        } else if (mediaItem.file_type === 'audio') {
            return <FileAudio className={iconClass} />;
        } else if (mediaItem.file_type === 'document') {
            return <FileText className={iconClass} />;
        }

        return <File className={iconClass} />;
    };

    // Handle thumbnail click
    const handleThumbnailClick = (mediaItem, e) => {
        if (e.shiftKey || selectedMedia.length > 0) {
            onMediaSelect(mediaItem.id);
        } else {
            setSelectedMediaItem(mediaItem);
            setShowDetailsModal(true);
        }
    };

    // Handle media actions
    const handleViewDetails = (mediaItem) => {
        setSelectedMediaItem(mediaItem);
        setShowDetailsModal(true);
    };

    const handleDownload = (mediaItem) => {
        window.open(mediaItem.url, '_blank');
    };

    const handleCopyUrl = async (mediaItem) => {
        try {
            await navigator.clipboard.writeText(mediaItem.full_url);
            // You could add a toast notification here
        } catch (err) {
            console.error('Failed to copy URL:', err);
        }
    };

    const handleDelete = async (mediaItem) => {
        if (!confirm(`Are you sure you want to delete "${mediaItem.original_filename}"?`)) {
            return;
        }

        try {
            await router.delete(route('admin.media.destroy', mediaItem.id));
        } catch (error) {
            console.error('Failed to delete media:', error);
        }
    };

    if (media.data.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center py-12">
                <File className="h-12 w-12 text-muted-foreground/50 mb-4" />
                <p className="text-muted-foreground font-medium">No media files found</p>
                <p className="text-sm text-muted-foreground">Upload some files to get started</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Selection Header */}
            {media.data.length > 0 && (
                <div className="flex items-center gap-2">
                    <Checkbox
                        checked={selectedMedia.length === media.data.length}
                        onCheckedChange={onSelectAll}
                        aria-label="Select all media"
                    />
                    <span className="text-sm text-muted-foreground">
                        Select all ({media.data.length} items)
                    </span>
                </div>
            )}

            {/* Media Grid - Fixed 150px width items */}
            <div className="grid gap-4" style={{ gridTemplateColumns: 'repeat(auto-fill, 150px)' }}>
                {media.data.map((mediaItem) => (
                    <Card key={mediaItem.id} className="w-[150px] overflow-hidden group">
                        <div className="relative">
                            {/* Selection Checkbox */}
                            <div className="absolute top-2 left-2 z-10">
                                <Checkbox
                                    checked={selectedMedia.includes(mediaItem.id)}
                                    onCheckedChange={() => onMediaSelect(mediaItem.id)}
                                    className="bg-background/80 backdrop-blur-sm"
                                />
                            </div>

                            {/* Action Menu */}
                            <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button
                                            variant="secondary"
                                            size="sm"
                                            className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm"
                                        >
                                            <MoreHorizontal className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => handleViewDetails(mediaItem)}>
                                            <Eye className="h-4 w-4 mr-2" />
                                            View Details
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleDownload(mediaItem)}>
                                            <Download className="h-4 w-4 mr-2" />
                                            Download
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleCopyUrl(mediaItem)}>
                                            <Copy className="h-4 w-4 mr-2" />
                                            Copy URL
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem
                                            onClick={() => handleDelete(mediaItem)}
                                            className="text-destructive"
                                        >
                                            <Trash2 className="h-4 w-4 mr-2" />
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                            {/* Thumbnail - Fixed 150x150 size */}
                            <div
                                className="w-[150px] h-[150px] bg-muted flex items-center justify-center cursor-pointer overflow-hidden"
                                onClick={(e) => handleThumbnailClick(mediaItem, e)}
                            >
                                {mediaItem.file_type === 'image' && mediaItem.thumbnail_url ? (
                                    <img
                                        src={mediaItem.thumbnail_url}
                                        alt={mediaItem.alt_text || mediaItem.original_filename}
                                        className="w-[150px] h-[150px] object-cover"
                                        onError={(e) => {
                                            console.error('Thumbnail failed to load, trying original:', mediaItem.thumbnail_url);
                                            // Fallback to original image
                                            e.target.src = mediaItem.url;
                                            e.target.onerror = (fallbackE) => {
                                                console.error('Original image also failed to load:', mediaItem.url);
                                                fallbackE.target.style.display = 'none';
                                                fallbackE.target.nextSibling.style.display = 'flex';
                                            };
                                        }}
                                    />
                                ) : null}
                                <div
                                    className="w-[150px] h-[150px] flex items-center justify-center"
                                    style={{ display: mediaItem.file_type === 'image' && mediaItem.thumbnail_url ? 'none' : 'flex' }}
                                >
                                    {getFileIcon(mediaItem)}
                                </div>
                            </div>
                        </div>

                        <CardContent className="p-2 w-[150px]">
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <h3 className="font-medium text-sm truncate">
                                            {mediaItem.title || mediaItem.original_filename}
                                        </h3>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{mediaItem.title || mediaItem.original_filename}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>

                            {mediaItem.description && (
                                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                    {mediaItem.description}
                                </p>
                            )}
                        </CardContent>

                        <CardFooter className="px-3 py-2 border-t flex justify-between text-xs text-muted-foreground">
                            <Badge variant="outline" className="h-5 text-xs">
                                {mediaItem.extension?.toUpperCase() || 'FILE'}
                            </Badge>
                            <span>{mediaItem.formatted_size}</span>
                        </CardFooter>
                    </Card>
                ))}
            </div>

            {/* Pagination */}
            {media.last_page > 1 && (
                <div className="flex justify-center">
                    <Pagination>
                        <PaginationContent>
                            {media.current_page > 1 && (
                                <PaginationItem>
                                    <PaginationPrevious
                                        href={route('admin.media.index', { ...route().params, page: media.current_page - 1 })}
                                    />
                                </PaginationItem>
                            )}

                            {Array.from({ length: Math.min(5, media.last_page) }, (_, i) => {
                                const page = i + 1;
                                return (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href={route('admin.media.index', { ...route().params, page })}
                                            isActive={page === media.current_page}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            })}

                            {media.current_page < media.last_page && (
                                <PaginationItem>
                                    <PaginationNext
                                        href={route('admin.media.index', { ...route().params, page: media.current_page + 1 })}
                                    />
                                </PaginationItem>
                            )}
                        </PaginationContent>
                    </Pagination>
                </div>
            )}

            {/* Details Modal */}
            {showDetailsModal && selectedMediaItem && (
                <MediaDetailsModal
                    isOpen={showDetailsModal}
                    onClose={() => {
                        setShowDetailsModal(false);
                        setSelectedMediaItem(null);
                    }}
                    media={selectedMediaItem}
                    onUpdate={(updatedMedia) => {
                        // Update the media in the list
                        router.reload({ only: ['media'] });
                    }}
                    userPermissions={userPermissions}
                />
            )}
        </div>
    );
}
