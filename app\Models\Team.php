<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\TeamType;
use Carbon\CarbonImmutable;
use Database\Factories\TeamFactory;
use <PERSON><PERSON>\Jetstream\Events\TeamCreated;
use <PERSON><PERSON>\Jetstream\Events\TeamDeleted;
use <PERSON><PERSON>\Jetstream\Events\TeamUpdated;
use Illuminate\Database\Eloquent\Collection;
use <PERSON><PERSON>\Jetstream\Team as JetstreamTeam;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property int $id
 * @property int $user_id
 * @property string $name
 * @property bool $personal_team
 * @property TeamType $team_type
 * @property string|null $license_number
 * @property string|null $license_state
 * @property string|null $address
 * @property string|null $phone
 * @property string|null $website
 * @property string|null $description
 * @property array|null $business_hours
 * @property array|null $service_areas
 * @property array|null $specialties
 * @property bool $has_team_subscription
 * @property int|null $max_agents
 * @property CarbonImmutable|null $created_at
 * @property CarbonImmutable|null $updated_at
 * @property-read User|null $owner
 * @property-read Collection<int, TeamInvitation> $teamInvitations
 * @property-read int|null $team_invitations_count
 * @property-read Membership|null $membership
 * @property-read Collection<int, User> $users
 * @property-read int|null $users_count
 *
 * @method static \Database\Factories\TeamFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team wherePersonalTeam($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereUserId($value)
 *
 * @mixin \Eloquent
 */
final class Team extends JetstreamTeam
{
    /** @use HasFactory<TeamFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'personal_team',
        'team_type',
        'license_number',
        'license_state',
        'address',
        'phone',
        'website',
        'description',
        'business_hours',
        'service_areas',
        'specialties',
        'has_team_subscription',
        'max_agents',
    ];

    /**
     * The event map for the model.
     *
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
        'updated' => TeamUpdated::class,
        'deleted' => TeamDeleted::class,
    ];

    /**
     * {@inheritdoc}
     *
     * @return HasMany<TeamInvitation, covariant $this>
     */
    public function teamInvitations(): HasMany
    {
        return parent::teamInvitations();
    }

    /**
     * Check if this is a real estate agency.
     */
    public function isAgency(): bool
    {
        return $this->team_type === TeamType::AGENCY;
    }

    /**
     * Check if this is a real estate brokerage.
     */
    public function isBrokerage(): bool
    {
        return $this->team_type === TeamType::BROKERAGE;
    }

    /**
     * Check if this is a real estate team.
     */
    public function isTeam(): bool
    {
        return $this->team_type === TeamType::TEAM;
    }

    /**
     * Check if the team requires a license.
     */
    public function requiresLicense(): bool
    {
        return $this->team_type->requiresLicense();
    }

    /**
     * Check if the team can add more agents.
     */
    public function canAddMoreAgents(): bool
    {
        if ($this->max_agents === null) {
            return true;
        }

        return $this->users()->count() < $this->max_agents;
    }

    /**
     * Get the number of available agent slots.
     */
    public function getAvailableAgentSlots(): ?int
    {
        if ($this->max_agents === null) {
            return null;
        }

        return max(0, $this->max_agents - $this->users()->count());
    }

    /**
     * Check if the team has a team subscription.
     */
    public function hasTeamSubscription(): bool
    {
        return $this->has_team_subscription;
    }

    /**
     * Get the team's display name with type.
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} ({$this->team_type->label()})";
    }

    /**
     * Get media files belonging to this company.
     */
    public function media(): HasMany
    {
        return $this->hasMany(Media::class, 'company_id');
    }

    /**
     * Get media collections belonging to this company.
     */
    public function mediaCollections(): HasMany
    {
        return $this->hasMany(MediaCollection::class, 'company_id');
    }

    /**
     * Get child agents belonging to this company.
     */
    public function childAgents(): HasMany
    {
        return $this->hasMany(User::class, 'parent_company_id');
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'personal_team' => 'boolean',
            'team_type' => TeamType::class,
            'business_hours' => 'array',
            'service_areas' => 'array',
            'specialties' => 'array',
            'has_team_subscription' => 'boolean',
        ];
    }
}
