<?php

declare(strict_types=1);

namespace App\Enums;

enum TeamType: string
{
    case AGENCY = 'agency';
    case BROKERAGE = 'brokerage';
    case TEAM = 'team';

    /**
     * Get the display name for the team type.
     */
    public function label(): string
    {
        return match ($this) {
            self::AGENCY => 'Real Estate Agency',
            self::BROKERAGE => 'Real Estate Brokerage',
            self::TEAM => 'Real Estate Team',
        };
    }

    /**
     * Get the description for the team type.
     */
    public function description(): string
    {
        return match ($this) {
            self::AGENCY => 'A real estate agency with multiple agents',
            self::BROKERAGE => 'A licensed real estate brokerage firm',
            self::TEAM => 'A specialized real estate team within an agency',
        };
    }

    /**
     * Get the maximum number of agents allowed by default.
     */
    public function defaultMaxAgents(): ?int
    {
        return match ($this) {
            self::AGENCY => 50,
            self::BROKERAGE => 100,
            self::TEAM => 10,
        };
    }

    /**
     * Check if this team type requires a license.
     */
    public function requiresLicense(): bool
    {
        return in_array($this, [self::AGENCY, self::BROKERAGE]);
    }

    /**
     * Get all team types as options for forms.
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn (self $type) => [$type->value => $type->label()])
            ->toArray();
    }
}
