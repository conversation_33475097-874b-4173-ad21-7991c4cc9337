<?php

declare(strict_types=1);

namespace App\Enums;

enum UserType: string
{
    case PLATFORM_ADMINISTRATOR = 'platform_administrator';
    case BROKERAGE_ADMIN = 'brokerage_admin';
    case COMPANY_AGENT = 'company_agent';
    case INDEPENDENT_AGENT = 'independent_agent';

    /**
     * Get the display name for the user type.
     */
    public function label(): string
    {
        return match ($this) {
            self::PLATFORM_ADMINISTRATOR => 'Platform Administrator',
            self::BROKERAGE_ADMIN => 'Brokerage Admin',
            self::COMPANY_AGENT => 'Company Agent',
            self::INDEPENDENT_AGENT => 'Independent Agent',
        };
    }

    /**
     * Get the description for the user type.
     */
    public function description(): string
    {
        return match ($this) {
            self::PLATFORM_ADMINISTRATOR => 'Highest level access with full system control',
            self::BROKERAGE_ADMIN => 'Company-level administrator who can manage their own company\'s agents',
            self::COMPANY_AGENT => 'Agent working under a specific brokerage/company',
            self::INDEPENDENT_AGENT => 'Individual agent with similar scope to Brokerage Admin but without company management capabilities',
        };
    }

    /**
     * Check if this user type can manage teams.
     */
    public function canManageTeams(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type requires team membership.
     */
    public function requiresTeamMembership(): bool
    {
        return $this === self::COMPANY_AGENT;
    }

    /**
     * Check if this user type can have individual subscriptions.
     */
    public function canHaveIndividualSubscription(): bool
    {
        return $this !== self::COMPANY_AGENT; // Company agents inherit from brokerage
    }

    /**
     * Check if this user type can access all media files.
     */
    public function canAccessAllMedia(): bool
    {
        return $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type can manage media for their company.
     */
    public function canManageCompanyMedia(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type has hierarchical access.
     */
    public function hasHierarchicalAccess(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type can manage agents.
     */
    public function canManageAgents(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type has company management capabilities.
     */
    public function canManageCompany(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type has platform-wide access.
     */
    public function hasPlatformAccess(): bool
    {
        return $this === self::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if this user type has similar access level to Brokerage Admin.
     */
    public function hasSimilarAccessToBrokerageAdmin(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::INDEPENDENT_AGENT;
    }

    /**
     * Get all user types as options for forms.
     *
     * @return array<string, string>
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn (self $type) => [$type->value => $type->label()])
            ->toArray();
    }
}
