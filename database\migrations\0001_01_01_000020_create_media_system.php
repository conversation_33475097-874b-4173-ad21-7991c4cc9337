<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create media folders table first (for foreign key reference)
        Schema::create('media_folders', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug');
            $table->text('path'); // Full path from root
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('company_id')->nullable();
            $table->boolean('is_public')->default(false);
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Foreign keys
            $table->foreign('parent_id')->references('id')->on('media_folders')->onDelete('set null');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('teams')->onDelete('cascade');

            // Indexes
            $table->index(['parent_id', 'company_id']);
            $table->index(['created_by', 'company_id']);
            $table->index('path');
            $table->index('is_public');
        });

        // Create main media table
        Schema::create('media', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('filename');
            $table->string('original_filename');
            $table->string('mime_type');
            $table->string('extension', 20);
            $table->bigInteger('size');
            $table->string('disk', 20)->default('public');
            $table->string('path', 500);
            $table->foreignId('uploaded_by')->constrained('users')->cascadeOnDelete();
            $table->foreignId('company_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->foreignId('folder_id')->nullable()->constrained('media_folders')->nullOnDelete();
            $table->boolean('is_public')->default(false);
            $table->string('alt_text')->nullable();
            $table->string('title')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index(['uploaded_by', 'company_id']);
            $table->index('extension');
            $table->index('is_public');
            $table->index('created_at');
            $table->index('folder_id');
        });

        // Create media metadata table for detailed file information
        Schema::create('media_metadata', function (Blueprint $table) {
            $table->id();
            $table->foreignId('media_id')->constrained('media')->cascadeOnDelete();

            // Image/Video dimensions
            $table->integer('width')->nullable();
            $table->integer('height')->nullable();

            // Video/Audio properties
            $table->integer('duration')->nullable(); // in seconds
            $table->integer('bitrate')->nullable(); // in kbps
            $table->float('framerate')->nullable();
            $table->integer('channels')->nullable(); // for audio

            // Camera/EXIF data
            $table->json('exif_data')->nullable();
            $table->string('color_profile')->nullable();
            $table->string('camera_make')->nullable();
            $table->string('camera_model')->nullable();
            $table->float('focal_length')->nullable();
            $table->float('aperture')->nullable();
            $table->integer('iso')->nullable();
            $table->float('shutter_speed')->nullable();

            // GPS coordinates
            $table->float('gps_latitude')->nullable();
            $table->float('gps_longitude')->nullable();

            // Additional metadata
            $table->json('keywords')->nullable();
            $table->json('additional_metadata')->nullable();

            $table->timestamps();

            $table->index('media_id');
        });

        // Create media collections table for organizing media
        Schema::create('media_collections', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('slug')->unique();
            $table->foreignId('created_by')->constrained('users')->cascadeOnDelete();
            $table->foreignId('company_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->boolean('is_public')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['created_by', 'company_id']);
            $table->index('is_public');
            $table->index('sort_order');
        });

        // Create media collection items pivot table
        Schema::create('media_collection_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('media_collection_id')->constrained('media_collections')->cascadeOnDelete();
            $table->foreignId('media_id')->constrained('media')->cascadeOnDelete();
            $table->integer('sort_order')->default(0);
            $table->timestamp('added_at')->useCurrent();
            $table->timestamps();

            $table->unique(['media_collection_id', 'media_id']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('media_collection_items');
        Schema::dropIfExists('media_collections');
        Schema::dropIfExists('media_metadata');
        Schema::dropIfExists('media');
        Schema::dropIfExists('media_folders');
    }
};
