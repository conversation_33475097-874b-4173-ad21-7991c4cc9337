<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\UserType;
use Carbon\CarbonImmutable;
use <PERSON><PERSON>\Cashier\Billable;
use <PERSON><PERSON>\Jetstream\HasTeams;
use <PERSON><PERSON>\Cashier\Subscription;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Database\Factories\UserFactory;
use Lara<PERSON>\Jetstream\HasProfilePhoto;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Database\Eloquent\Collection;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Spatie\Permission\Traits\HasRoles;

use function Illuminate\Events\queueable;

/**
 * @property int $id
 * @property string $name
 * @property string $email
 * @property CarbonImmutable|null $email_verified_at
 * @property UserType $user_type
 * @property string|null $license_number
 * @property string|null $license_state
 * @property CarbonImmutable|null $license_expiry
 * @property string|null $phone
 * @property string|null $bio
 * @property array|null $specialties
 * @property string|null $company_name
 * @property string|null $company_license
 * @property string|null $company_address
 * @property string|null $company_website
 * @property bool $inherits_team_subscription
 * @property bool $profile_completed
 * @property CarbonImmutable|null $onboarding_completed_at
 * @property string $password
 * @property string|null $remember_token
 * @property int|null $current_team_id
 * @property string|null $profile_photo_path
 * @property CarbonImmutable|null $created_at
 * @property CarbonImmutable|null $updated_at
 * @property string|null $deleted_at
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property string|null $two_factor_confirmed_at
 * @property string|null $stripe_id
 * @property string|null $pm_type
 * @property string|null $pm_last_four
 * @property string|null $trial_ends_at
 * @property-read Team|null $currentTeam
 * @property-read DatabaseNotificationCollection<int, DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Collection<int, OauthConnection> $oauthConnections
 * @property-read int|null $oauth_connections_count
 * @property-read Collection<int, Team> $ownedTeams
 * @property-read int|null $owned_teams_count
 * @property-read Collection<int, Team> $ownedTeamsBase
 * @property-read int|null $owned_teams_base_count
 * @property-read string $profile_photo_url
 * @property-read Collection<int, Subscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read Membership|null $membership
 * @property-read Collection<int, Team> $teams
 * @property-read int|null $teams_count
 * @property-read Collection<int, PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User hasExpiredGenericTrial()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User onGenericTrial()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCurrentTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePmLastFour($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePmType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereProfilePhotoPath($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereStripeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTrialEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorConfirmedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorRecoveryCodes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereTwoFactorSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
final class User extends Authenticatable implements MustVerifyEmail
{
    use Billable;
    use HasApiTokens;

    /** @use HasFactory<UserFactory> */
    use HasFactory;

    use HasProfilePhoto;
    use HasTeams  {
        ownedTeams as public ownedTeamsBase;
    }
    use Notifiable;
    use TwoFactorAuthenticatable;
    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $guarded = [
        'id',
        'created_at',
        'updated_at',

    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
        'stripe_id',
        'pm_type',
        'pm_last_four',
        'trial_ends_at',
    ];

    /**
     * Get the team that the invitation belongs to.
     *
     * @return HasMany<Team, covariant $this>
     */
    public function ownedTeams(): HasMany
    {
        return $this->ownedTeamsBase();
    }

    /**
     * Get the Oauth Connections for the user.
     *
     * @return HasMany<OauthConnection, covariant $this>
     */
    public function oauthConnections(): HasMany
    {
        return $this->hasMany(OauthConnection::class);
    }



    /**
     * Check if the user can access admin features.
     */
    public function canAccessAdmin(): bool
    {
        return $this->isPlatformAdministrator();
    }

    protected static function booted(): void
    {
        self::updated(queueable(function (User $customer): void {
            if ($customer->hasStripeId()) {
                $customer->syncStripeCustomerDetails();
            }
        }));
    }

    /**
     * Check if the user is a company administrator.
     * @deprecated Use isBrokerageAdmin() instead
     */
    public function isCompanyAdmin(): bool
    {
        return $this->user_type === UserType::BROKERAGE_ADMIN;
    }

    /**
     * Check if the user is an individual agent.
     * @deprecated Use isIndependentAgent() instead
     */
    public function isIndividualAgent(): bool
    {
        return $this->user_type === UserType::INDEPENDENT_AGENT;
    }

    /**
     * Check if the user is a super admin.
     * @deprecated Use isPlatformAdministrator() instead
     */
    public function isSuperAdmin(): bool
    {
        return $this->user_type === UserType::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if the user is a child agent.
     * @deprecated Use isCompanyAgent() instead
     */
    public function isChildAgent(): bool
    {
        return $this->user_type === UserType::COMPANY_AGENT;
    }

    /**
     * Get the parent company for child agents.
     */
    public function parentCompany(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'parent_company_id');
    }

    /**
     * Get media files uploaded by this user.
     */
    public function uploadedMedia(): HasMany
    {
        return $this->hasMany(Media::class, 'uploaded_by');
    }

    /**
     * Get the profile image media.
     */
    public function profileImage(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'profile_image_id');
    }

    /**
     * Get accessible media based on user type and permissions.
     */
    public function getAccessibleMedia()
    {
        $query = Media::query();

        if ($this->user_type->canAccessAllMedia()) {
            // Super admin can access all media
            return $query;
        }

        if ($this->user_type->canManageCompanyMedia() && $this->currentTeam) {
            // Company admin can access their own media and their company's media
            return $query->where(function ($q) {
                $q->where('uploaded_by', $this->id)
                  ->orWhere('company_id', $this->currentTeam->id);
            });
        }

        if ($this->user_type === UserType::COMPANY_AGENT && $this->currentTeam) {
            // Child agents can access their own media and company media if allowed
            return $query->where(function ($q) {
                $q->where('uploaded_by', $this->id);

                if ($this->can_access_company_media) {
                    $q->orWhere('company_id', $this->currentTeam->id);
                }
            });
        }

        // Individual agents can only access their own media
        return $query->where('uploaded_by', $this->id);
    }

    /**
     * Check if the user is a team agent (added by company).
     */
    public function isTeamAgent(): bool
    {
        // Team agents are users who belong to a team but don't own it
        return !$this->isCompanyAdmin() &&
               !$this->isIndividualAgent() &&
               $this->allTeams()->where('personal_team', false)->count() > 0;
    }

    /**
     * Check if the user can manage teams.
     */
    public function canManageTeams(): bool
    {
        return $this->user_type->canManageTeams();
    }

    /**
     * Check if the user requires team membership.
     */
    public function requiresTeamMembership(): bool
    {
        return $this->user_type->requiresTeamMembership();
    }

    /**
     * Check if the user can have individual subscriptions.
     */
    public function canHaveIndividualSubscription(): bool
    {
        return $this->user_type->canHaveIndividualSubscription();
    }

    /**
     * Check if the user's license is expired.
     */
    public function isLicenseExpired(): bool
    {
        return $this->license_expiry && $this->license_expiry->isPast();
    }

    /**
     * Check if the user has completed their profile.
     */
    public function hasCompletedProfile(): bool
    {
        return $this->profile_completed;
    }

    /**
     * Check if the user has completed onboarding.
     */
    public function hasCompletedOnboarding(): bool
    {
        return $this->onboarding_completed_at !== null;
    }

    /**
     * Mark the user's profile as completed.
     */
    public function markProfileCompleted(): void
    {
        $this->update([
            'profile_completed' => true,
            'onboarding_completed_at' => now(),
        ]);
    }

    /**
     * Get the user's display name with company if applicable.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isCompanyAdmin() && $this->company_name) {
            return "{$this->name} ({$this->company_name})";
        }

        return $this->name;
    }

    /**
     * Get the profile image URL with fallback to old profile photo.
     */
    public function getProfileImageUrlAttribute(): ?string
    {
        return \App\Services\MediaHelper::getProfileImageUrl(
            $this->profile_image_id,
            $this->profile_photo_path
        );
    }

    /**
     * Get the profile image thumbnail URL.
     */
    public function getProfileThumbnailUrlAttribute(): ?string
    {
        return \App\Services\MediaHelper::getProfileThumbnailUrl(
            $this->profile_image_id,
            $this->profile_photo_path
        );
    }

    /**
     * Set the profile image by media ID.
     */
    public function setProfileImage(?int $mediaId): void
    {
        $this->update(['profile_image_id' => $mediaId]);
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'user_type' => UserType::class,
            'license_expiry' => 'date',
            'specialties' => 'array',
            'inherits_team_subscription' => 'boolean',
            'profile_completed' => 'boolean',
            'onboarding_completed_at' => 'datetime',
        ];
    }

    // Role-based helper methods for the new hierarchy

    /**
     * Check if user is a Platform Administrator.
     */
    public function isPlatformAdministrator(): bool
    {
        return $this->user_type === UserType::PLATFORM_ADMINISTRATOR;
    }

    /**
     * Check if user is a Brokerage Admin.
     */
    public function isBrokerageAdmin(): bool
    {
        return $this->user_type === UserType::BROKERAGE_ADMIN;
    }

    /**
     * Check if user is an Independent Agent.
     */
    public function isIndependentAgent(): bool
    {
        return $this->user_type === UserType::INDEPENDENT_AGENT;
    }

    /**
     * Check if user is a Company Agent.
     */
    public function isCompanyAgent(): bool
    {
        return $this->user_type === UserType::COMPANY_AGENT;
    }

    /**
     * Check if user can manage agents (Brokerage Admin or Platform Admin).
     */
    public function canManageAgents(): bool
    {
        return $this->user_type->canManageAgents();
    }

    /**
     * Check if user can manage companies (Brokerage Admin or Platform Admin).
     */
    public function canManageCompanies(): bool
    {
        return $this->user_type->canManageCompany();
    }

    /**
     * Check if user has platform-wide access.
     */
    public function hasPlatformAccess(): bool
    {
        return $this->user_type->hasPlatformAccess();
    }

    /**
     * Check if user has similar access level to Brokerage Admin.
     */
    public function hasSimilarAccessToBrokerageAdmin(): bool
    {
        return $this->user_type->hasSimilarAccessToBrokerageAdmin();
    }

    /**
     * Get users that this user can manage based on role hierarchy.
     */
    public function getManagedUsers()
    {
        if ($this->isPlatformAdministrator()) {
            // Platform Admin can manage all users
            return User::query();
        }

        if ($this->isBrokerageAdmin()) {
            // Brokerage Admin can manage users in their company
            return User::where('current_team_id', $this->current_team_id)
                ->orWhere('parent_company_id', $this->current_team_id);
        }

        // Independent Agent and Company Agent can only manage themselves
        return User::where('id', $this->id);
    }

    /**
     * Check if user can manage a specific user.
     */
    public function canManageUser(User $targetUser): bool
    {
        if ($this->isPlatformAdministrator()) {
            return true;
        }

        if ($this->isBrokerageAdmin()) {
            // Can manage users in same company or child agents
            return $targetUser->current_team_id === $this->current_team_id ||
                   $targetUser->parent_company_id === $this->current_team_id;
        }

        // Can only manage themselves
        return $this->id === $targetUser->id;
    }

    /**
     * Check if user can access company-level features.
     */
    public function canAccessCompanyFeatures(): bool
    {
        return in_array($this->user_type, [
            UserType::PLATFORM_ADMINISTRATOR,
            UserType::BROKERAGE_ADMIN,
            UserType::COMPANY_AGENT,
        ]);
    }

    /**
     * Get the company/team this user belongs to for scoping purposes.
     */
    public function getScopeCompanyId(): ?int
    {
        if ($this->isPlatformAdministrator()) {
            return null; // No scope restriction
        }

        return $this->current_team_id;
    }
}

