<?php

namespace App\Console\Commands;

use App\Models\Media;
use App\Services\ThumbnailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class GenerateThumbnails extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'media:generate-thumbnails
                            {--all : Generate thumbnails for all media}
                            {--media-id=* : Generate thumbnails for specific media IDs}
                            {--size=* : Generate only specific thumbnail sizes}
                            {--force : Regenerate existing thumbnails}
                            {--missing-only : Generate only missing thumbnails}';

    /**
     * The console command description.
     */
    protected $description = 'Generate media thumbnails. Use --all to process all media, or specify media IDs and sizes.';

    protected ThumbnailService $thumbnailService;

    public function __construct(ThumbnailService $thumbnailService)
    {
        parent::__construct();
        $this->thumbnailService = $thumbnailService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🖼️  Media Thumbnail Generation Tool');
        $this->newLine();

        $mediaIds = $this->option('media-id');
        $sizes = $this->option('size');
        $generateAll = $this->option('all');
        $force = $this->option('force');
        $missingOnly = $this->option('missing-only');

        // Get media to process
        if ($generateAll) {
            $media = Media::all()->filter(function ($mediaItem) {
                return $mediaItem->isImage();
            });
            $this->info("Processing all image files ({$media->count()} total)");
        } elseif (!empty($mediaIds)) {
            $media = Media::whereIn('id', $mediaIds)->get()->filter(function ($mediaItem) {
                return $mediaItem->isImage();
            });
            $this->info("Processing specific media files: " . implode(', ', $mediaIds));
        } else {
            $this->error('❌ Please specify --all or provide --media-id options');
            return self::FAILURE;
        }

        if ($media->isEmpty()) {
            $this->warn('⚠️  No image files found to process');
            return self::SUCCESS;
        }

        // Get available sizes
        $availableSizes = $this->thumbnailService->getAvailableSizes();

        if (!empty($sizes)) {
            // Validate specified sizes
            $invalidSizes = array_diff($sizes, $availableSizes);
            if (!empty($invalidSizes)) {
                $this->error('❌ Invalid thumbnail sizes: ' . implode(', ', $invalidSizes));
                $this->info('Available sizes: ' . implode(', ', $availableSizes));
                return self::FAILURE;
            }
            $sizesToGenerate = $sizes;
        } else {
            $sizesToGenerate = $availableSizes;
        }

        $this->info('Thumbnail sizes to generate: ' . implode(', ', $sizesToGenerate));

        if ($force) {
            $this->warn('🔄 FORCE MODE - Existing thumbnails will be regenerated');
        } elseif ($missingOnly) {
            $this->info('📋 MISSING ONLY MODE - Only missing thumbnails will be generated');
        }

        $this->newLine();

        $generatedCount = 0;
        $skippedCount = 0;
        $errorCount = 0;
        $totalCount = 0;

        $progressBar = $this->output->createProgressBar($media->count());
        $progressBar->start();

        foreach ($media as $mediaItem) {
            // Check if original file exists
            if (!Storage::disk($mediaItem->disk)->exists($mediaItem->path)) {
                $this->newLine();
                $this->warn("⚠️  Original file not found: {$mediaItem->path}");
                $errorCount++;
                $progressBar->advance();
                continue;
            }

            foreach ($sizesToGenerate as $size) {
                $sizeConfig = $this->thumbnailService->getSizeConfig($size);
                if (!$sizeConfig) {
                    continue;
                }

                $totalCount++;
                $thumbnailPath = $this->thumbnailService->getThumbnailPath($mediaItem, $size, $sizeConfig);
                $thumbnailExists = Storage::disk($mediaItem->disk)->exists($thumbnailPath);

                // Skip if thumbnail exists and not forcing regeneration
                if ($thumbnailExists && !$force) {
                    $skippedCount++;
                    continue;
                }

                // Skip if missing-only mode and thumbnail exists
                if ($missingOnly && $thumbnailExists) {
                    $skippedCount++;
                    continue;
                }

                try {
                    $result = $this->thumbnailService->generateThumbnail($mediaItem, $size, $sizeConfig);
                    if ($result) {
                        $generatedCount++;
                    } else {
                        $errorCount++;
                    }
                } catch (\Exception $e) {
                    $this->newLine();
                    $this->error("❌ Failed to generate {$size} thumbnail for media {$mediaItem->id}: " . $e->getMessage());
                    $errorCount++;
                }
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("✅ GENERATION COMPLETED:");
        $this->info("🖼️  Thumbnails generated: {$generatedCount}");
        $this->info("⏭️  Thumbnails skipped: {$skippedCount}");
        $this->info("❌ Errors encountered: {$errorCount}");
        $this->info("📊 Total thumbnails processed: {$totalCount}");

        $this->newLine();

        if ($errorCount > 0) {
            $this->warn("⚠️  {$errorCount} errors occurred during generation. Check the logs for details.");
        }

        $this->info('🎉 Thumbnail generation process completed!');

        return $errorCount > 0 ? self::FAILURE : self::SUCCESS;
    }
}
