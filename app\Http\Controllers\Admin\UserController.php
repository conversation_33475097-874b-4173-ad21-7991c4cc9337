<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Enums\UserType;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'userTypes' => [
                ['value' => 'all', 'label' => 'All Types'],
                ['value' => 'independent_agent', 'label' => 'Independent Agent'],
                ['value' => 'brokerage_admin', 'label' => 'Brokerage Admin'],
                ['value' => 'company_agent', 'label' => 'Company Agent'],
                ['value' => 'platform_administrator', 'label' => 'Platform Administrator'],
            ],
        ]);
    }

    /**
     * API endpoint for DataTable
     */
    public function apiIndex(Request $request)
    {
        $search = $request->get('search');
        $perPage = $request->get('per_page', 10);
        $sortColumn = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        // Handle filters
        $filterName = $request->get('filter_name');
        $filterEmail = $request->get('filter_email');
        $filterUserType = $request->get('filter_user_type');
        $filterCompany = $request->get('filter_company_name');
        $filterCreatedFrom = $request->get('filter_created_at_from');
        $filterCreatedTo = $request->get('filter_created_at_to');

        $query = User::query();

        // Apply search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company_name', 'like', "%{$search}%");
            });
        }

        // Apply filters
        if ($filterName) {
            $query->where('name', 'like', "%{$filterName}%");
        }

        if ($filterEmail) {
            $query->where('email', 'like', "%{$filterEmail}%");
        }

        if ($filterUserType && $filterUserType !== 'all') {
            $query->where('user_type', $filterUserType);
        }

        if ($filterCompany) {
            $query->where('company_name', 'like', "%{$filterCompany}%");
        }

        if ($filterCreatedFrom) {
            $query->whereDate('created_at', '>=', $filterCreatedFrom);
        }

        if ($filterCreatedTo) {
            $query->whereDate('created_at', '<=', $filterCreatedTo);
        }

        // Apply sorting
        $allowedSortColumns = ['name', 'email', 'user_type', 'company_name', 'created_at', 'email_verified_at'];
        if (in_array($sortColumn, $allowedSortColumns)) {
            $query->orderBy($sortColumn, $sortDirection);
        } else {
            $query->latest();
        }

        $users = $query->paginate($perPage);

        // Transform users to add computed properties
        $users->getCollection()->transform(function ($user) {
            // Add is_super_admin flag
            $user->is_super_admin = $user->canAccessAdmin();

            // Add company_display - teams feature disabled
            $user->company_display = $user->company_name ?: '-';

            // Add user type label
            $user->user_type_label = $user->user_type ? $user->user_type->label() : 'Unknown';

            return $user;
        });

        return response()->json($users);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'userTypes' => [
                ['value' => 'independent_agent', 'label' => 'Independent Agent'],
                ['value' => 'brokerage_admin', 'label' => 'Brokerage Admin'],
                ['value' => 'company_agent', 'label' => 'Company Agent'],
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'user_type' => ['required', Rule::enum(UserType::class)],
            'phone' => ['nullable', 'string', 'max:20'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'company_address' => ['nullable', 'string', 'max:500'],
            'license_number' => ['nullable', 'string', 'max:50'],
            'license_state' => ['nullable', 'string', 'max:2'],
            'license_expiry' => ['nullable', 'date'],
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'user_type' => $validated['user_type'],
            'phone' => $validated['phone'] ?? null,
            'company_name' => $validated['company_name'] ?? null,
            'company_address' => $validated['company_address'] ?? null,
            'license_number' => $validated['license_number'] ?? null,
            'license_state' => $validated['license_state'] ?? null,
            'license_expiry' => $validated['license_expiry'] ?? null,
            'email_verified_at' => now(),
        ]);

        // Create team for brokerage admin
        if ($user->user_type === UserType::BROKERAGE_ADMIN) {
            $teamName = $user->company_name ?: ($user->name . "'s Company");
            $team = $user->ownedTeams()->create([
                'name' => $teamName,
                'personal_team' => false,
                'team_type' => 'agency',
            ]);
            $user->switchTeam($team);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): Response
    {
        // Teams feature disabled - no team relationships loaded
        // $user->load([
        //     'currentTeam',
        //     'ownedTeams',
        //     'teams' => function ($query) {
        //         $query->withPivot('role', 'created_at');
        //     }
        // ]);

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user): Response
    {
        // Load profile image relationship
        $user->load('profileImage');

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
            'userTypes' => [
                ['value' => 'independent_agent', 'label' => 'Independent Agent'],
                ['value' => 'brokerage_admin', 'label' => 'Brokerage Admin'],
                ['value' => 'company_agent', 'label' => 'Company Agent'],
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'user_type' => ['required', Rule::enum(UserType::class)],
            'phone' => ['nullable', 'string', 'max:20'],
            'company_name' => ['nullable', 'string', 'max:255'],
            'company_address' => ['nullable', 'string', 'max:500'],
            'license_number' => ['nullable', 'string', 'max:50'],
            'license_state' => ['nullable', 'string', 'max:2'],
            'license_expiry' => ['nullable', 'date'],
            'profile_image_id' => ['nullable', 'exists:media,id'],
        ]);

        $user->update($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        // Prevent deleting the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }
}
