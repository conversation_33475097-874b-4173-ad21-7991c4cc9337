import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogDescription,
} from '@/components/shadcn/ui/dialog';
import { Button } from '@/components/shadcn/ui/button';
import { Progress } from '@/components/shadcn/ui/progress';
import { Alert, AlertDescription } from '@/components/shadcn/ui/alert';
import { Card, CardContent } from '@/components/shadcn/ui/card';
import { ScrollArea } from '@/components/shadcn/ui/scroll-area';
import { Badge } from '@/components/shadcn/ui/badge';
import { Checkbox } from '@/components/shadcn/ui/checkbox';
import { Label } from '@/components/shadcn/ui/label';
import {
    Upload,
    X,
    CheckCircle,
    AlertCircle,
    File as FileIcon,
    ImageIcon,
    FileTextIcon,
    FileVideoIcon,
    FileAudioIcon,
    StopCircle,
} from 'lucide-react';

export default function MediaUploader({ isOpen, onClose, onUploadComplete, uploadRoute, currentFolder }) {
    const [files, setFiles] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState({});
    const [uploadResults, setUploadResults] = useState(null);


    // File type icons
    const getFileIcon = (file) => {
        const type = file.type.split('/')[0];
        const iconClass = "h-8 w-8 text-muted-foreground";

        switch (type) {
            case 'image':
                return <ImageIcon className={iconClass} />;
            case 'video':
                return <FileVideoIcon className={iconClass} />;
            case 'audio':
                return <FileAudioIcon className={iconClass} />;
            case 'text':
            case 'application':
                return <FileTextIcon className={iconClass} />;
            default:
                return <FileIcon className={iconClass} />;
        }
    };

    // Format file size
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Dropzone configuration
    const onDrop = useCallback((acceptedFiles) => {
        const newFiles = acceptedFiles.map(file => ({
            file,
            id: Math.random().toString(36).substr(2, 9),
            status: 'pending', // pending, uploading, success, error
            progress: 0,
            error: null
        }));

        setFiles(prev => [...prev, ...newFiles]);
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: {
            'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
            'video/*': ['.mp4', '.webm', '.ogg', '.mov', '.avi'],
            'audio/*': ['.mp3', '.wav', '.ogg', '.aac', '.flac'],
            'application/pdf': ['.pdf'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
            'application/vnd.ms-excel': ['.xls'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'text/plain': ['.txt']
        },
        maxSize: 50 * 1024 * 1024, // 50MB
        multiple: true
    });

    // Remove file from list
    const removeFile = (fileId) => {
        setFiles(prev => prev.filter(f => f.id !== fileId));
    };

    // Upload files
    const handleUpload = async () => {
        if (files.length === 0) return;

        setUploading(true);
        setUploadResults(null);

        const formData = new FormData();
        files.forEach(({ file }) => {
            formData.append('files[]', file);
        });


        // Add folder_id if we're in a folder
        if (currentFolder?.id) {
            formData.append('folder_id', currentFolder.id);
        }

        try {
            // Use provided uploadRoute or default to admin route
            const storeRoute = uploadRoute || route('admin.media.store');
            const response = await axios.post(storeRoute, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
                onUploadProgress: (progressEvent) => {
                    const progress = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    setUploadProgress({ overall: progress });
                }
            });

            setUploadResults(response.data);

            if (response.data.success) {
                setTimeout(() => {
                    onUploadComplete();
                }, 1500);
            }
        } catch (error) {
            console.error('Upload failed:', error);

            let errorMessages = ['Upload failed. Please try again.'];

            if (error.response) {
                // Server responded with error status
                console.error('Response data:', error.response.data);
                console.error('Response status:', error.response.status);

                if (error.response.data?.errors) {
                    // Laravel validation errors
                    const validationErrors = error.response.data.errors;
                    errorMessages = Object.values(validationErrors).flat();
                } else if (error.response.data?.message) {
                    // General error message
                    errorMessages = [error.response.data.message];
                } else if (error.response.status === 422) {
                    errorMessages = ['Validation failed. Please check your files and try again.'];
                } else if (error.response.status === 413) {
                    errorMessages = ['File too large. Maximum size is 50MB per file.'];
                } else if (error.response.status === 500) {
                    errorMessages = ['Server error. Please try again later.'];
                }
            } else if (error.request) {
                // Network error
                console.error('Network error:', error.request);
                errorMessages = ['Network error. Please check your connection and try again.'];
            }

            setUploadResults({
                success: false,
                errors: errorMessages
            });
        } finally {
            setUploading(false);
        }
    };

    // Reset state when modal closes
    const handleClose = () => {
        if (!uploading) {
            setFiles([]);
            setUploadProgress({});
            setUploadResults(null);

            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle>Upload Media Files</DialogTitle>
                    <DialogDescription>
                        Upload images, videos, audio files, and documents to your media library.
                        {currentFolder && (
                            <span className="block mt-1 text-sm font-medium text-blue-600">
                                📁 Uploading to: {currentFolder.name}
                            </span>
                        )}
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4 flex-1 overflow-hidden">
                    {/* Upload Results */}
                    {uploadResults && (
                        <Alert className={uploadResults.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                            <div className="flex items-center gap-2">
                                {uploadResults.success ? (
                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                ) : (
                                    <AlertCircle className="h-4 w-4 text-red-600" />
                                )}
                                <AlertDescription>
                                    {uploadResults.success
                                        ? `Successfully uploaded ${uploadResults.uploaded?.length || 0} file(s)`
                                        : 'Upload failed'
                                    }
                                </AlertDescription>
                            </div>
                            {uploadResults.errors && uploadResults.errors.length > 0 && (
                                <div className="mt-2 space-y-1">
                                    {uploadResults.errors.map((error, index) => (
                                        <p key={index} className="text-sm text-red-600">
                                            {typeof error === 'string' ? error : error.error}
                                        </p>
                                    ))}
                                </div>
                            )}
                        </Alert>
                    )}

                    {/* Upload Progress */}
                    {uploading && (
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">Uploading files...</span>
                                <span className="text-sm text-muted-foreground">
                                    {uploadProgress.overall || 0}%
                                </span>
                            </div>
                            <Progress value={uploadProgress.overall || 0} />
                        </div>
                    )}

                    {/* Dropzone */}
                    {!uploading && !uploadResults && (
                        <div
                            {...getRootProps()}
                            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                                isDragActive
                                    ? 'border-primary bg-primary/5'
                                    : 'border-muted-foreground/25 hover:border-primary/50'
                            }`}
                        >
                            <input {...getInputProps()} />
                            <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-lg font-medium mb-2">
                                {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
                            </p>
                            <p className="text-sm text-muted-foreground mb-4">
                                or click to browse files
                            </p>
                            <p className="text-xs text-muted-foreground">
                                Supports images, videos, audio, and documents up to 50MB
                            </p>
                        </div>
                    )}

                    {/* File List */}
                    {files.length > 0 && !uploadResults && (
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="font-medium">Files to Upload ({files.length})</h3>

                            </div>

                            <ScrollArea className="h-48">
                                <div className="space-y-2">
                                    {files.map((fileItem) => (
                                        <Card key={fileItem.id}>
                                            <CardContent className="p-3">
                                                <div className="flex items-center gap-3">
                                                    <div className="flex-shrink-0">
                                                        {getFileIcon(fileItem.file)}
                                                    </div>
                                                    <div className="flex-1 min-w-0 overflow-hidden">
                                                        <p className="font-medium text-sm truncate" title={fileItem.file.name}>
                                                            {fileItem.file.name}
                                                        </p>
                                                        <div className="flex items-center gap-2 mt-1">
                                                            <p className="text-xs text-muted-foreground">
                                                                {formatFileSize(fileItem.file.size)}
                                                            </p>
                                                            <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                                                                {fileItem.file.type.split('/')[0]}
                                                            </Badge>
                                                        </div>
                                                    </div>
                                                    <div className="flex-shrink-0">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeFile(fileItem.id)}
                                                            disabled={uploading}
                                                            className="h-8 w-8 p-0"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            </ScrollArea>
                        </div>
                    )}
                </div>

                <DialogFooter className="flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="flex gap-2 w-full justify-end">
                        <Button variant="outline" onClick={handleClose} disabled={uploading}>
                            {uploadResults ? 'Close' : 'Cancel'}
                        </Button>
                        {files.length > 0 && !uploadResults && (
                            <Button onClick={handleUpload} disabled={uploading}>
                                {uploading ? 'Uploading...' : `Upload ${files.length} file(s)`}
                            </Button>
                        )}
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
