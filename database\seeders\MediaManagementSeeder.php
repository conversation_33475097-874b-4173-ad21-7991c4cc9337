<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Team;
use App\Enums\UserType;
use Illuminate\Support\Facades\Hash;

class MediaManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Platform Administrator
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Platform Administrator',
                'password' => Hash::make('password'),
                'user_type' => UserType::PLATFORM_ADMINISTRATOR,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => true,
            ]
        );

        // Create Brokerage Admin with Team
        $companyAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Brokerage Admin',
                'password' => Hash::make('password'),
                'user_type' => UserType::BROKERAGE_ADMIN,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => true,
                'company_name' => 'ABC Real Estate',
            ]
        );

        // Create Company Team
        $companyTeam = Team::firstOrCreate(
            ['name' => 'ABC Real Estate'],
            [
                'user_id' => $companyAdmin->id,
                'personal_team' => false,
            ]
        );

        $companyAdmin->update(['current_team_id' => $companyTeam->id]);

        // Create Independent Agent
        $individualAgent = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Independent Agent',
                'password' => Hash::make('password'),
                'user_type' => UserType::INDEPENDENT_AGENT,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => false,
            ]
        );

        // Create Company Agent
        $childAgent = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Company Agent',
                'password' => Hash::make('password'),
                'user_type' => UserType::COMPANY_AGENT,
                'email_verified_at' => now(),
                'can_upload_media' => true,
                'can_access_company_media' => true,
                'current_team_id' => $companyTeam->id,
                'parent_company_id' => $companyTeam->id,
            ]
        );

        // Add child agent to company team
        $companyTeam->users()->attach($childAgent->id);

        // Create sample media collections
        // Note: MediaCollection model needs to be created first
        /*
        $publicCollection = MediaCollection::firstOrCreate(
            ['slug' => 'public-assets'],
            [
                'name' => 'Public Assets',
                'description' => 'Publicly accessible media files',
                'created_by' => $superAdmin->id,
                'sort_order' => 1,
            ]
        );

        $companyCollection = MediaCollection::firstOrCreate(
            ['slug' => 'company-assets'],
            [
                'name' => 'Company Assets',
                'description' => 'ABC Real Estate company media',
                'created_by' => $companyAdmin->id,
                'company_id' => $companyTeam->id,
                'sort_order' => 2,
            ]
        );
        */

        $this->command->info('Media management structure seeded successfully!');
        $this->command->info('Users created:');
        $this->command->info('- Platform Administrator: <EMAIL>');
        $this->command->info('- Brokerage Admin: <EMAIL>');
        $this->command->info('- Independent Agent: <EMAIL>');
        $this->command->info('- Company Agent: <EMAIL>');
        $this->command->info('Password for all users: password');
    }
}
