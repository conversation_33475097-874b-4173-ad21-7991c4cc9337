import React, { useState } from 'react';
import { router } from '@inertiajs/react';
import axios from 'axios';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/Components/shadcn/ui/dialog';
import { Button } from '@/Components/shadcn/ui/button';
import { ScrollArea } from '@/Components/shadcn/ui/scroll-area';
import { Card, CardContent } from '@/Components/shadcn/ui/card';
import { Badge } from '@/Components/shadcn/ui/badge';
import { 
    Folder, 
    FolderOpen, 
    Home, 
    Loader2,
    Move
} from 'lucide-react';
import { useToast } from '@/Components/hooks/use-toast';

export default function MoveModal({ 
    isOpen, 
    onClose, 
    item = null,
    itemType = 'media', // 'media' or 'folder'
    folders = [],
    currentFolder = null,
    onMoveComplete 
}) {
    const [selectedFolder, setSelectedFolder] = useState(null);
    const [isMoving, setIsMoving] = useState(false);
    const { toast } = useToast();

    const handleMove = async () => {
        if (!item) return;
        
        setIsMoving(true);

        try {
            const isCompanyContext = window.location.pathname.includes('/company/');
            
            if (itemType === 'media') {
                const routeName = isCompanyContext ? 'company.media.update' : 'admin.media.update';
                
                await axios.put(route(routeName, item.id), {
                    folder_id: selectedFolder?.id || null
                });
                
                toast({
                    title: "Success",
                    description: `File moved to ${selectedFolder ? selectedFolder.name : 'root folder'}`,
                });
            } else {
                // Moving folder
                const routeName = isCompanyContext ? 'company.media-folders.update' : 'admin.media-folders.update';
                
                await axios.put(route(routeName, item.id), {
                    parent_id: selectedFolder?.id || null
                });
                
                toast({
                    title: "Success", 
                    description: `Folder moved to ${selectedFolder ? selectedFolder.name : 'root folder'}`,
                });
            }

            onClose();
            
            if (onMoveComplete) {
                onMoveComplete();
            } else {
                router.reload();
            }
        } catch (error) {
            toast({
                title: "Error",
                description: error.response?.data?.message || "Failed to move item",
                variant: "destructive",
            });
        } finally {
            setIsMoving(false);
        }
    };

    const handleClose = () => {
        if (!isMoving) {
            setSelectedFolder(null);
            onClose();
        }
    };

    // Filter out current folder and item itself if moving a folder
    const availableFolders = folders.filter(folder => {
        if (itemType === 'folder' && item) {
            return folder.id !== item.id && folder.parent_id !== item.id;
        }
        return true;
    });

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Move className="w-5 h-5" />
                        Move {itemType === 'media' ? 'File' : 'Folder'}
                    </DialogTitle>
                    <DialogDescription>
                        Choose a destination folder for "{item?.original_filename || item?.name}"
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    {/* Root Folder Option */}
                    <Card 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                            selectedFolder === null ? 'ring-2 ring-primary' : ''
                        }`}
                        onClick={() => setSelectedFolder(null)}
                    >
                        <CardContent className="p-3">
                            <div className="flex items-center space-x-3">
                                <Home className="w-8 h-8 text-gray-500" />
                                <div>
                                    <p className="font-medium">Root Folder</p>
                                    <p className="text-sm text-muted-foreground">Move to main directory</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Available Folders */}
                    {availableFolders.length > 0 && (
                        <ScrollArea className="h-64">
                            <div className="space-y-2">
                                {availableFolders.map((folder) => (
                                    <Card
                                        key={folder.id}
                                        className={`cursor-pointer transition-all hover:shadow-md ${
                                            selectedFolder?.id === folder.id ? 'ring-2 ring-primary' : ''
                                        }`}
                                        onClick={() => setSelectedFolder(folder)}
                                    >
                                        <CardContent className="p-3">
                                            <div className="flex items-center space-x-3">
                                                <div className="relative">
                                                    <Folder className="w-8 h-8 text-blue-500" />
                                                    {folder.media_count > 0 && (
                                                        <Badge
                                                            variant="secondary"
                                                            className="absolute -top-1 -right-1 text-xs px-1"
                                                        >
                                                            {folder.media_count}
                                                        </Badge>
                                                    )}
                                                </div>
                                                <div className="flex-1">
                                                    <p className="font-medium">{folder.name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {folder.media_count || 0} items
                                                    </p>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </ScrollArea>
                    )}

                    {availableFolders.length === 0 && (
                        <div className="text-center py-8 text-muted-foreground">
                            <Folder className="w-12 h-12 mx-auto mb-2 opacity-50" />
                            <p>No folders available</p>
                        </div>
                    )}
                </div>

                <DialogFooter>
                    <Button
                        type="button"
                        variant="outline"
                        onClick={handleClose}
                        disabled={isMoving}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleMove}
                        disabled={isMoving || !item}
                    >
                        {isMoving && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                        Move Here
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
