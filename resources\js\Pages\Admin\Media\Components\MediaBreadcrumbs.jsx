import React from 'react';
import { router } from '@inertiajs/react';
import { But<PERSON> } from '@/Components/shadcn/ui/button';
import { ChevronRight, Home, Folder } from 'lucide-react';

export default function MediaBreadcrumbs({ breadcrumbs = [], currentFolder }) {
    const handleNavigate = (folderId = null) => {
        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        const params = folderId ? { folder_id: folderId } : {};
        router.get(route(routeName), params);
    };

    return (
        <div className="flex items-center space-x-1 text-sm">
            {/* Home/Root */}
            <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate()}
                className="h-8 px-2"
            >
                <Home className="w-4 h-4 mr-1" />
                Media Library
            </Button>

            {/* Breadcrumb Trail */}
            {breadcrumbs.map((folder, index) => (
                <React.Fragment key={folder.id}>
                    <ChevronRight className="w-4 h-4 text-muted-foreground" />
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleNavigate(folder.id)}
                        className="h-8 px-2"
                        disabled={index === breadcrumbs.length - 1} // Disable current folder
                    >
                        <Folder className="w-4 h-4 mr-1" />
                        {folder.name}
                    </Button>
                </React.Fragment>
            ))}
        </div>
    );
}
