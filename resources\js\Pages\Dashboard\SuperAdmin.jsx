import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import StatsCard from '@/Components/StatsCard'
import AppLayout from '@/Layouts/AppLayout'
import { Icon } from '@iconify/react'
import { Head, Link } from '@inertiajs/react'
import { route } from 'ziggy-js'

const defaultRecentUsers = []
const defaultRecentTeams = []

export default function SuperAdminDashboard({ user, stats, recent_users = defaultRecentUsers, recent_teams = defaultRecentTeams }) {
  const quickActions = [
    {
      title: 'User Management',
      description: 'Manage all users',
      icon: 'lucide:users',
      href: route('admin.users.index'),
      color: 'bg-blue-500',
    },
    {
      title: 'Plan Management',
      description: 'Manage subscription plans',
      icon: 'lucide:credit-card',
      href: route('admin.plans.index'),
      color: 'bg-green-500',
    },
    {
      title: 'System Settings',
      description: 'Configure system settings',
      icon: 'lucide:cog',
      href: '/admin/settings',
      color: 'bg-purple-500',
    },
  ]

  const systemStats = [
    {
      title: 'Total Users',
      value: stats.total_users,
      icon: 'lucide:users',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Total Teams',
      value: stats.total_teams,
      icon: 'lucide:building',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Individual Agents',
      value: stats.total_individual_agents,
      icon: 'lucide:user',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Companies',
      value: stats.total_companies,
      icon: 'lucide:building-2',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <AppLayout
      title="Super Admin Dashboard"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Super Admin Dashboard
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Welcome back,
              {' '}
              {user.name}
              ! Manage your real estate platform.
            </p>
          </div>
          <Badge variant="destructive" className="text-sm">
            <Icon icon="lucide:shield-check" className="mr-1 h-4 w-4" />
            Super Admin
          </Badge>
        </div>
      )}
    >
      <Head title="Super Admin Dashboard" />

      <div className="py-12">
        <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
          {/* System Stats */}
          <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {systemStats.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                color={stat.color}
                bgColor={stat.bgColor}
              />
            ))}
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <h2 className="mb-4 text-xl font-semibold text-gray-900 dark:text-white">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action, index) => (
                <Card key={index} className="transition-all hover:shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`rounded-lg p-3 ${action.color}`}>
                        <Icon icon={action.icon} className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Link href={action.href}>
                        <Button className="w-full">
                          Access
                          <Icon icon="lucide:arrow-right" className="ml-2 h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Recent Users */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Users</CardTitle>
                    <CardDescription>Latest registered users</CardDescription>
                  </div>
                  <Link
                    href={route('admin.users.index')}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    View All
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recent_users.length > 0
                    ? (
                        recent_users.map(recentUser => (
                          <div key={recentUser.id} className="flex items-center space-x-4">
                            <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                              <Icon icon="lucide:user" className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 dark:text-white">
                                {recentUser.name}
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {recentUser.email}
                              </p>
                            </div>
                            <Badge variant="outline">
                              {recentUser.user_type?.replace('_', ' ') || 'N/A'}
                            </Badge>
                          </div>
                        ))
                      )
                    : (
                        <div className="text-center py-8">
                          <Icon icon="lucide:users" className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-gray-600 dark:text-gray-400">No recent users</p>
                        </div>
                      )}
                </div>
              </CardContent>
            </Card>

            {/* Recent Teams */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Recent Teams</CardTitle>
                    <CardDescription>Latest created teams</CardDescription>
                  </div>
                  <Link
                    href="/admin/teams"
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    View All
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recent_teams.length > 0
                    ? (
                        recent_teams.map(team => (
                          <div key={team.id} className="flex items-center space-x-4">
                            <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                              <Icon icon="lucide:building" className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 dark:text-white">
                                {team.name}
                              </p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {team.team_type?.replace('_', ' ') || 'Team'}
                              </p>
                            </div>
                            <Badge variant={team.personal_team ? 'secondary' : 'default'}>
                              {team.personal_team ? 'Personal' : 'Business'}
                            </Badge>
                          </div>
                        ))
                      )
                    : (
                        <div className="text-center py-8">
                          <Icon icon="lucide:building" className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-gray-600 dark:text-gray-400">No recent teams</p>
                        </div>
                      )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* System Health */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>System Overview</CardTitle>
              <CardDescription>Platform health and quick access</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                    <Icon icon="lucide:check-circle" className="h-6 w-6 text-green-600" />
                  </div>
                  <p className="mt-2 font-medium text-gray-900 dark:text-white">System Status</p>
                  <p className="text-sm text-green-600">All systems operational</p>
                </div>
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <Icon icon="lucide:database" className="h-6 w-6 text-blue-600" />
                  </div>
                  <p className="mt-2 font-medium text-gray-900 dark:text-white">Database</p>
                  <p className="text-sm text-blue-600">Connected & healthy</p>
                </div>
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                    <Icon icon="lucide:activity" className="h-6 w-6 text-purple-600" />
                  </div>
                  <p className="mt-2 font-medium text-gray-900 dark:text-white">Performance</p>
                  <p className="text-sm text-purple-600">Optimal</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
