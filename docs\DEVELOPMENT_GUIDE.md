# 🚀 Larasonic Development Guide

## Quick Start

### Prerequisites

- PHP 8.3+
- Node.js 18+
- Composer
- Docker (optional but recommended)

### Installation

#### Option 1: Docker (Recommended)

```bash
# Clone the repository
git clone https://github.com/shipfastlabs/larasonic-react.git
cd larasonic-react

# Install PHP dependencies
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php83-composer:latest \
    composer install --ignore-platform-reqs

# Setup environment
cp .env.example .env
./vendor/bin/sail up -d
./vendor/bin/sail composer run setup
```

#### Option 2: Local Development

```bash
# Clone and install
git clone https://github.com/shipfastlabs/larasonic-react.git
cd larasonic-react
composer install
npm install

# Setup environment
cp .env.example .env
composer run setup
```

### Development Server

```bash
# Start all development services
composer run dev

# This starts:
# - <PERSON>vel server (http://localhost:8000)
# - Queue worker
# - Log viewer (Pail)
# - Vite dev server (HMR)
```

## Understanding the Stack

### Laravel + Inertia.js Workflow

#### 1. Creating a New Page

```bash
# 1. Add route
# routes/web.php
Route::get('/agents', [AgentController::class, 'index'])->name('agents.index');

# 2. Create controller
php artisan make:controller AgentController
```

```php
// app/Http/Controllers/AgentController.php
public function index()
{
    return Inertia::render('Agents/Index', [
        'agents' => Agent::with('team')->paginate(10),
        'filters' => request()->only(['search', 'status'])
    ]);
}
```

```jsx
// resources/js/Pages/Agents/Index.jsx
import AppLayout from '@/Layouts/AppLayout'
import { usePage } from '@inertiajs/react'

export default function Index({ agents, filters }) {
  return (
    <AppLayout title="Agents">
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Agents</h1>
        {/* Agent list implementation */}
      </div>
    </AppLayout>
  )
}
```

#### 2. Form Handling Pattern

```jsx
import { Button } from '@/Components/shadcn/ui/button'
import { Input } from '@/Components/shadcn/ui/input'
import { useForm } from '@inertiajs/react'

export default function CreateAgent() {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    email: '',
    license_number: ''
  })

  const submit = (e) => {
    e.preventDefault()
    post('/agents', {
      onSuccess: () => {
        // Redirect handled by Laravel
        // Flash message available via props.flash
      }
    })
  }

  return (
    <form onSubmit={submit} className="space-y-4">
      <div>
        <Input
          placeholder="Agent Name"
          value={data.name}
          onChange={e => setData('name', e.target.value)}
        />
        {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
      </div>

      <Button type="submit" disabled={processing}>
        {processing ? 'Creating...' : 'Create Agent'}
      </Button>
    </form>
  )
}
```

### React Component Patterns

#### 1. Layout Usage

```jsx
// For authenticated pages
import AppLayout from '@/Layouts/AppLayout'

export default function Dashboard() {
    return (
        <AppLayout title="Dashboard">
            {/* Your content */}
        </AppLayout>
    )
}

// For public pages
import WebLayout from '@/Layouts/WebLayout'

export default function About() {
    return (
        <WebLayout>
            {/* Your content */}
        </WebLayout>
    )
}
```

#### 2. Accessing Shared Data

```jsx
import { usePage } from '@inertiajs/react'

export default function Navigation() {
  const { props } = usePage()
  const user = props.auth.user

  return (
    <nav>
      {user
        ? (
            <span>
              Welcome,
              {user.name}
            </span>
          )
        : (
            <Link href="/login">Login</Link>
          )}
    </nav>
  )
}
```

#### 3. SEO Management

```jsx
import { useSeoMetaTags } from '@/Composables/useSeoMetaTags'

export default function ProductPage({ product }) {
  useSeoMetaTags({
    title: product.name,
    description: product.description,
    ogImage: product.image_url
  })

  return <div>{/* Product content */}</div>
}
```

### Component Library (shadcn/ui)

#### Installation of New Components

```bash
# Add a new shadcn/ui component
npx shadcn-ui@latest add dialog

# This creates: resources/js/Components/shadcn/ui/dialog.jsx
```

#### Usage Example

```jsx
import { Button } from '@/Components/shadcn/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/Components/shadcn/ui/dialog'

export default function CreateAgentDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Create Agent</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Agent</DialogTitle>
          <DialogDescription>
            Add a new agent to your team.
          </DialogDescription>
        </DialogHeader>
        {/* Form content */}
      </DialogContent>
    </Dialog>
  )
}
```

## Backend Development

### Model Patterns

```php
// app/Models/Agent.php
class Agent extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'license_number',
        'user_type'
    ];

    protected function casts(): array
    {
        return [
            'user_type' => UserType::class,
            'license_expiry' => 'date',
        ];
    }

    // Relationships
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // Business logic methods
    public function isLicenseExpired(): bool
    {
        return $this->license_expiry?->isPast() ?? false;
    }
}
```

### Controller Best Practices

```php
class AgentController extends Controller
{
    public function index(Request $request)
    {
        $agents = Agent::query()
            ->when($request->search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%");
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->with('team')
            ->paginate(10)
            ->withQueryString(); // Preserve query parameters

        return Inertia::render('Agents/Index', [
            'agents' => $agents,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    public function store(StoreAgentRequest $request)
    {
        $agent = Agent::create($request->validated());

        return redirect()
            ->route('agents.index')
            ->with('success', 'Agent created successfully!');
    }
}
```

### Form Request Validation

```php
// app/Http/Requests/StoreAgentRequest.php
class StoreAgentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:agents'],
            'license_number' => ['required', 'string', 'unique:agents'],
            'user_type' => ['required', Rule::enum(UserType::class)],
        ];
    }

    public function messages(): array
    {
        return [
            'license_number.unique' => 'This license number is already registered.',
        ];
    }
}
```

## Testing

### Backend Testing with Pest

```php
// tests/Feature/AgentTest.php
test('can create agent', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)
        ->post('/agents', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'license_number' => 'LIC123',
            'user_type' => 'individual_agent',
        ]);

    $response->assertRedirect('/agents');
    $this->assertDatabaseHas('agents', ['email' => '<EMAIL>']);
});

test('validates required fields', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)
        ->post('/agents', []);

    $response->assertSessionHasErrors(['name', 'email', 'license_number']);
});
```

### Frontend Testing (Future)

```jsx
import AgentCard from '@/Components/AgentCard'
// tests/Components/AgentCard.test.jsx
import { render, screen } from '@testing-library/react'

test('displays agent information', () => {
  const agent = {
    name: 'John Doe',
    email: '<EMAIL>',
    license_number: 'LIC123'
  }

  render(<AgentCard agent={agent} />)

  expect(screen.getByText('John Doe')).toBeInTheDocument()
  expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
})
```

## Code Quality

### PHP Standards

```bash
# Format code
./vendor/bin/pint

# Static analysis
./vendor/bin/phpstan analyse

# Modernize code
./vendor/bin/rector
```

### JavaScript/React Standards

```bash
# Lint and fix
npm run lint:fix

# Type checking (if TypeScript is added)
npm run type-check
```

## Deployment

### Production Build

```bash
# Build assets
npm run build

# Optimize Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Docker Production

```bash
# Build production image
docker build -t larasonic:latest .

# Run with environment variables
docker run -d \
    -p 80:8000 \
    -e APP_ENV=production \
    -e DB_CONNECTION=mysql \
    larasonic:latest
```

## Common Patterns

### Loading States

```jsx
import { router } from '@inertiajs/react'
import { useState } from 'react'

export default function RefreshButton() {
  const [loading, setLoading] = useState(false)

  const refresh = () => {
    setLoading(true)
    router.reload({
      onFinish: () => setLoading(false)
    })
  }

  return (
    <Button onClick={refresh} disabled={loading}>
      {loading ? 'Refreshing...' : 'Refresh'}
    </Button>
  )
}
```

### Error Handling

```jsx
import { Alert, AlertDescription } from '@/Components/shadcn/ui/alert'
import { usePage } from '@inertiajs/react'

export default function FlashMessages() {
  const { props } = usePage()
  const { success, error } = props.flash

  return (
    <>
      {success && (
        <Alert className="mb-4">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </>
  )
}
```

This guide provides the foundation for developing with the Larasonic stack. The combination of Laravel's robust backend with Inertia.js and React creates a powerful, productive development experience.
