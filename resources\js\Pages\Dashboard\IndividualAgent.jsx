import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import StatsCard from '@/Components/StatsCard'
import AppLayout from '@/Layouts/AppLayout'
import { Icon } from '@iconify/react'
import { Link } from '@inertiajs/react'
import { route } from 'ziggy-js'

export default function IndividualAgentDashboard({ user, stats }) {
  const quickActions = [
    {
      title: 'Add New Listing',
      description: 'Create a new property listing',
      icon: 'lucide:home-plus',
      href: '#', // TODO: Add route when listings feature is implemented
      color: 'bg-blue-500',
    },
    {
      title: 'Manage Leads',
      description: 'View and follow up on leads',
      icon: 'lucide:users',
      href: '#', // TODO: Add route when leads feature is implemented
      color: 'bg-green-500',
    },
    {
      title: 'View Reports',
      description: 'Check your performance metrics',
      icon: 'lucide:bar-chart-3',
      href: '#', // TODO: Add route when reports feature is implemented
      color: 'bg-purple-500',
    },
    {
      title: 'Update Profile',
      description: 'Manage your agent profile',
      icon: 'lucide:user-cog',
      href: route('profile.show'),
      color: 'bg-orange-500',
    },
  ]

  const recentActivity = [
    {
      type: 'listing',
      title: 'New listing created',
      description: '123 Main St, Anytown',
      time: '2 hours ago',
      icon: 'lucide:home',
    },
    {
      type: 'lead',
      title: 'New lead received',
      description: 'John Doe - Looking for 3BR house',
      time: '4 hours ago',
      icon: 'lucide:user-plus',
    },
    {
      type: 'appointment',
      title: 'Showing scheduled',
      description: '456 Oak Ave - Tomorrow 2:00 PM',
      time: '1 day ago',
      icon: 'lucide:calendar',
    },
  ]

  return (
    <AppLayout title="Agent Dashboard">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Welcome back,
              {' '}
              {user.name}
              !
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Here's what's happening with your real estate business today.
            </p>
          </div>
          <Badge variant="outline" className="text-sm">
            Individual Agent
          </Badge>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatsCard
            value={stats.active_listings}
            description="Active Listings"
            icon="lucide:home"
          />
          <StatsCard
            value={stats.pending_leads}
            description="Pending Leads"
            icon="lucide:users"
          />
          <StatsCard
            value={stats.closed_deals}
            description="Closed Deals"
            icon="lucide:handshake"
          />
          <StatsCard
            value={`$${stats.total_commission.toLocaleString()}`}
            description="Total Commission"
            icon="lucide:dollar-sign"
          />
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icon icon="lucide:zap" className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Common tasks to help you manage your business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map(action => (
                <Link
                  key={action.title}
                  href={action.href}
                  className="group block"
                >
                  <div className="rounded-lg border p-4 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800">
                    <div className="flex items-center gap-3">
                      <div className={`rounded-lg p-2 ${action.color} text-white`}>
                        <Icon icon={action.icon} className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon icon="lucide:activity" className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your latest business activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="rounded-full bg-gray-100 p-2 dark:bg-gray-800">
                      <Icon icon={activity.icon} className="h-4 w-4" />
                    </div>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Getting Started */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon icon="lucide:rocket" className="h-5 w-5" />
                Getting Started
              </CardTitle>
              <CardDescription>
                Complete these steps to set up your profile
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-green-100 p-1 dark:bg-green-900">
                    <Icon icon="lucide:check" className="h-3 w-3 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-sm">Account created</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                    <Icon icon="lucide:circle" className="h-3 w-3" />
                  </div>
                  <span className="text-sm">Complete your profile</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                    <Icon icon="lucide:circle" className="h-3 w-3" />
                  </div>
                  <span className="text-sm">Add your first listing</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-gray-100 p-1 dark:bg-gray-800">
                    <Icon icon="lucide:circle" className="h-3 w-3" />
                  </div>
                  <span className="text-sm">Set up lead capture</span>
                </div>
                <Button className="w-full mt-4" variant="outline">
                  <Icon icon="lucide:arrow-right" className="mr-2 h-4 w-4" />
                  Continue Setup
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
