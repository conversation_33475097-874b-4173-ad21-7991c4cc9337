<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MediaFolder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class MediaFolderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $parentId = $request->get('parent_id');

        $folders = MediaFolder::accessibleBy($user)
            ->where('parent_id', $parentId)
            ->with(['creator', 'company', 'parent'])
            ->withCount(['mediaFiles', 'subfolders'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'folders' => $folders,
            'parent_id' => $parentId,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\-_\.()]+$/', // Allow alphanumeric, spaces, hyphens, underscores, dots, parentheses
            ],
            'parent_id' => 'nullable|exists:media_folders,id',
            'description' => 'nullable|string|max:1000',
        ], [
            'name.regex' => 'Folder name can only contain letters, numbers, spaces, hyphens, underscores, dots, and parentheses.',
        ]);

        $user = $request->user();

        // Check if user can create folders in parent
        if ($request->parent_id) {
            $parent = MediaFolder::findOrFail($request->parent_id);
            if (!$parent->canBeAccessedBy($user)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You cannot create folders in this location.'
                ], 403);
            }
        }

        // Generate unique name and slug
        $originalName = $this->cleanFolderName(trim($request->name));
        $uniqueName = $this->generateUniqueFolderName($originalName, $request->parent_id, $user->currentTeam?->id);
        $uniqueSlug = $this->generateUniqueFolderSlug($originalName, $request->parent_id, $user->currentTeam?->id);

        $folder = MediaFolder::create([
            'name' => $uniqueName,
            'slug' => $uniqueSlug,
            'parent_id' => $request->parent_id,
            'created_by' => $user->id,
            'company_id' => $user->currentTeam?->id,
            'description' => $request->description,
        ]);

        // Create physical directory
        $folder->createPhysicalDirectory();

        return response()->json([
            'success' => true,
            'message' => 'Folder created successfully.',
            'folder' => $folder->load(['creator', 'company', 'parent'])
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(MediaFolder $mediaFolder): JsonResponse
    {
        $this->authorize('view', $mediaFolder);

        return response()->json([
            'folder' => $mediaFolder->load(['creator', 'company', 'parent', 'subfolders', 'mediaFiles'])
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MediaFolder $mediaFolder): JsonResponse
    {
        $this->authorize('update', $mediaFolder);

        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\-_\.()]+$/', // Allow alphanumeric, spaces, hyphens, underscores, dots, parentheses
            ],
            'description' => 'nullable|string|max:1000',
        ], [
            'name.regex' => 'Folder name can only contain letters, numbers, spaces, hyphens, underscores, dots, and parentheses.',
        ]);

        $oldPath = $mediaFolder->path;
        $originalName = $this->cleanFolderName(trim($request->name));

        // Only generate unique name if the name is actually changing
        if ($mediaFolder->name !== $originalName) {
            $uniqueName = $this->generateUniqueFolderNameForUpdate($originalName, $mediaFolder->parent_id, $mediaFolder->company_id, $mediaFolder->id);
            $uniqueSlug = $this->generateUniqueFolderSlugForUpdate($originalName, $mediaFolder->parent_id, $mediaFolder->company_id, $mediaFolder->id);
        } else {
            $uniqueName = $mediaFolder->name;
            $uniqueSlug = $mediaFolder->slug;
        }

        $mediaFolder->update([
            'name' => $uniqueName,
            'slug' => $uniqueSlug,
            'description' => $request->description,
        ]);

        // If path changed, rename physical directory
        if ($oldPath !== $mediaFolder->path) {
            $oldFullPath = storage_path('app/public/media/' . $oldPath);
            $newFullPath = storage_path('app/public/media/' . $mediaFolder->path);

            if (file_exists($oldFullPath)) {
                rename($oldFullPath, $newFullPath);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Folder updated successfully.',
            'folder' => $mediaFolder->fresh(['creator', 'company', 'parent'])
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MediaFolder $mediaFolder): JsonResponse
    {
        $this->authorize('delete', $mediaFolder);

        try {
            // Delete all media files in this folder first (load them explicitly)
            $mediaFiles = $mediaFolder->mediaFiles()->get();
            foreach ($mediaFiles as $media) {
                $media->delete(); // This will delete the file and all thumbnails
            }

            // Recursively delete all subfolders (load them explicitly)
            $subfolders = $mediaFolder->subfolders()->get();
            foreach ($subfolders as $subfolder) {
                $this->deleteFolder($subfolder);
            }

            // Remove physical directory (should be empty now)
            $fullPath = storage_path('app/public/media/' . $mediaFolder->path);
            if (file_exists($fullPath) && is_dir($fullPath)) {
                // Use recursive directory removal
                $this->removeDirectory($fullPath);
            }

            // Delete the folder record
            $mediaFolder->delete();

            return response()->json([
                'success' => true,
                'message' => 'Folder and all its contents deleted successfully.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to delete folder', [
                'folder_id' => $mediaFolder->id,
                'folder_path' => $mediaFolder->path,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Provide more specific error messages based on the error type
            $errorMessage = 'Failed to delete folder. Please try again.';

            if (str_contains($e->getMessage(), 'lazy load')) {
                $errorMessage = 'Database error occurred while deleting folder. Please try again.';
            } elseif (str_contains($e->getMessage(), 'Permission denied')) {
                $errorMessage = 'Permission denied. Unable to delete folder files.';
            } elseif (str_contains($e->getMessage(), 'No such file')) {
                $errorMessage = 'Some files in the folder could not be found. Folder may be partially deleted.';
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage,
                'error_details' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get folder breadcrumbs.
     */
    public function breadcrumbs(Request $request): JsonResponse
    {
        $folderId = $request->get('folder_id');

        if (!$folderId) {
            return response()->json(['breadcrumbs' => []]);
        }

        $folder = MediaFolder::findOrFail($folderId);
        $this->authorize('view', $folder);

        $ancestors = $folder->getAncestors();
        $ancestors[] = $folder;

        return response()->json([
            'breadcrumbs' => $ancestors
        ]);
    }

    /**
     * Generate unique folder name by adding number suffix if needed.
     */
    private function generateUniqueFolderName(string $name, ?int $parentId, ?int $companyId): string
    {
        $originalName = $name;
        $counter = 1;

        while ($this->folderNameExists($name, $parentId, $companyId)) {
            $name = $originalName . ' (' . $counter . ')';
            $counter++;
        }

        return $name;
    }

    /**
     * Generate unique folder slug by adding number suffix if needed.
     */
    private function generateUniqueFolderSlug(string $name, ?int $parentId, ?int $companyId): string
    {
        $originalSlug = Str::slug($name);
        $slug = $originalSlug;
        $counter = 1;

        while ($this->folderSlugExists($slug, $parentId, $companyId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if folder name exists in the same parent and company.
     */
    private function folderNameExists(string $name, ?int $parentId, ?int $companyId): bool
    {
        return MediaFolder::where('name', $name)
            ->where('parent_id', $parentId)
            ->where('company_id', $companyId)
            ->exists();
    }

    /**
     * Check if folder slug exists in the same parent and company.
     */
    private function folderSlugExists(string $slug, ?int $parentId, ?int $companyId): bool
    {
        return MediaFolder::where('slug', $slug)
            ->where('parent_id', $parentId)
            ->where('company_id', $companyId)
            ->exists();
    }

    /**
     * Generate unique folder name for update (excluding current folder).
     */
    private function generateUniqueFolderNameForUpdate(string $name, ?int $parentId, ?int $companyId, int $excludeId): string
    {
        $originalName = $name;
        $counter = 1;

        while ($this->folderNameExistsForUpdate($name, $parentId, $companyId, $excludeId)) {
            $name = $originalName . ' (' . $counter . ')';
            $counter++;
        }

        return $name;
    }

    /**
     * Generate unique folder slug for update (excluding current folder).
     */
    private function generateUniqueFolderSlugForUpdate(string $name, ?int $parentId, ?int $companyId, int $excludeId): string
    {
        $originalSlug = Str::slug($name);
        $slug = $originalSlug;
        $counter = 1;

        while ($this->folderSlugExistsForUpdate($slug, $parentId, $companyId, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if folder name exists for update (excluding current folder).
     */
    private function folderNameExistsForUpdate(string $name, ?int $parentId, ?int $companyId, int $excludeId): bool
    {
        return MediaFolder::where('name', $name)
            ->where('parent_id', $parentId)
            ->where('company_id', $companyId)
            ->where('id', '!=', $excludeId)
            ->exists();
    }

    /**
     * Check if folder slug exists for update (excluding current folder).
     */
    private function folderSlugExistsForUpdate(string $slug, ?int $parentId, ?int $companyId, int $excludeId): bool
    {
        return MediaFolder::where('slug', $slug)
            ->where('parent_id', $parentId)
            ->where('company_id', $companyId)
            ->where('id', '!=', $excludeId)
            ->exists();
    }

    /**
     * Clean folder name by removing extra spaces and invalid characters.
     */
    private function cleanFolderName(string $name): string
    {
        // Remove extra spaces and trim
        $name = preg_replace('/\s+/', ' ', trim($name));

        // Remove any leading/trailing dots or hyphens
        $name = trim($name, '.-');

        // Ensure it's not empty after cleaning
        if (empty($name)) {
            $name = 'New Folder';
        }

        return $name;
    }

    /**
     * Recursively delete a folder and all its contents.
     */
    private function deleteFolder(MediaFolder $folder): void
    {
        // Delete all media files in this folder (load them explicitly)
        $mediaFiles = $folder->mediaFiles()->get();
        foreach ($mediaFiles as $media) {
            $media->delete(); // This will delete the file and all thumbnails
        }

        // Recursively delete all subfolders (load them explicitly)
        $subfolders = $folder->subfolders()->get();
        foreach ($subfolders as $subfolder) {
            $this->deleteFolder($subfolder);
        }

        // Remove physical directory
        $fullPath = storage_path('app/public/media/' . $folder->path);
        if (file_exists($fullPath) && is_dir($fullPath)) {
            $this->removeDirectory($fullPath);
        }

        // Delete the folder record
        $folder->delete();
    }

    /**
     * Recursively remove a directory and all its contents.
     */
    private function removeDirectory(string $path): bool
    {
        if (!is_dir($path)) {
            return false;
        }

        $files = array_diff(scandir($path), ['.', '..']);

        foreach ($files as $file) {
            $filePath = $path . DIRECTORY_SEPARATOR . $file;

            if (is_dir($filePath)) {
                $this->removeDirectory($filePath);
            } else {
                unlink($filePath);
            }
        }

        return rmdir($path);
    }
}
