import { createInertiaApp } from '@inertiajs/react'
// import './bootstrap'

import { createHead, UnheadProvider } from '@unhead/react/client'
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers'

import { createRoot } from 'react-dom/client'
import '../css/app.css'

const appName = import.meta.env.VITE_APP_NAME || 'Larasonic'

// Create a global head instance with Capo plugin
const head = createHead()

// Initialize theme on app load
if (typeof window !== 'undefined') {
  const savedTheme = localStorage.getItem('theme')
  const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  const isDark = savedTheme === 'dark' || (!savedTheme && systemDark)

  document.documentElement.classList.toggle('dark', isDark)
  if (!savedTheme) {
    localStorage.setItem('theme', isDark ? 'dark' : 'light')
  }
}

createInertiaApp({
  title: title => `${title} - ${appName}`,
  resolve: name =>
    resolvePageComponent(
      `./Pages/${name}.jsx`,
      import.meta.glob('./Pages/**/*.jsx'),
    ),
  setup({ el, App, props }) {
    const root = createRoot(el)
    root.render(
      <>
        <UnheadProvider head={head}>
          <App {...props} />
        </UnheadProvider>
      </>,
    )
  },
  progress: {
    color: '#4B5563',
  },
})
