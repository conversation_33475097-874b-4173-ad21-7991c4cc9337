# Media Selector Component Usage Guide

This guide explains how to use the MediaSelector component and MediaHelper service to link media files to other models in your Laravel/Inertia.js application.

## Overview

The MediaSelector component provides a reusable way to select media files from your media library and link them to any model using media IDs instead of storing file paths directly. This approach provides better scalability and flexibility.

## Components

### 1. MediaSelector Component

A reusable React component for selecting media files with a popup/modal interface.

**Location:** `resources/js/Components/MediaSelector.jsx`

### 2. MediaPickerModal Component

The modal component that displays media files with upload, folder creation, and delete options.

**Location:** `resources/js/Components/MediaPickerModal.jsx`

### 3. MediaHelper Service

A service class with helper functions for retrieving media URLs, handling different sizes, and managing media relationships.

**Location:** `app/Services/MediaHelper.php`

### 4. MediaHelper Facade

A facade for easier access to MediaHelper methods.

**Location:** `app/Facades/MediaHelper.php`

## Basic Usage

### Step 1: Add Media ID Field to Your Model

Create a migration to add a media ID field to your model:

```php
// Example migration for adding profile_image_id to users table
Schema::table('users', function (Blueprint $table) {
    $table->foreignId('profile_image_id')->nullable()->constrained('media')->nullOnDelete();
    $table->index('profile_image_id');
});
```

### Step 2: Add Relationship to Your Model

Add the media relationship to your Eloquent model:

```php
// In your model (e.g., User.php)
use Illuminate\Database\Eloquent\Relations\BelongsTo;

public function profileImage(): BelongsTo
{
    return $this->belongsTo(Media::class, 'profile_image_id');
}

// Optional: Add helper methods
public function getProfileImageUrlAttribute(): ?string
{
    return \App\Services\MediaHelper::getProfileImageUrl(
        $this->profile_image_id,
        $this->fallback_path // optional fallback
    );
}
```

### Step 3: Use MediaSelector in Your Form

Import and use the MediaSelector component in your React form:

```jsx
import MediaSelector from '@/Components/MediaSelector';

export default function YourForm({ model }) {
    const form = useForm({
        // ... other fields
        media_id: model.media_id || null,
    });

    const handleMediaSelect = (mediaId) => {
        form.setData('media_id', mediaId);
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Other form fields */}
            
            <MediaSelector
                label="Select Image"
                value={form.data.media_id}
                onChange={handleMediaSelect}
                selectedMedia={model.media} // Pass the loaded media relationship
                accept="image/*" // Optional: filter by file type
                placeholder="Choose an image"
                description="Select an image from your media library"
                error={form.errors.media_id}
                required={true}
            />
        </form>
    );
}
```

### Step 4: Update Your Controller/Action

Handle the media ID in your controller or form action:

```php
// In your controller or form action
public function update(Request $request, YourModel $model)
{
    $validated = $request->validate([
        // ... other validation rules
        'media_id' => ['nullable', 'exists:media,id'],
    ]);

    $model->update([
        // ... other fields
        'media_id' => $validated['media_id'],
    ]);

    return redirect()->back();
}
```

## MediaSelector Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | string | - | Label for the field |
| `value` | number\|null | - | Selected media ID |
| `onChange` | function | - | Callback when media is selected/changed |
| `selectedMedia` | object\|null | null | Currently selected media object |
| `accept` | string | 'all' | File types to accept ('image/*', 'video/*', 'audio/*', 'document/*', 'all') |
| `required` | boolean | false | Whether the field is required |
| `placeholder` | string | 'Click to select media' | Placeholder text |
| `description` | string | null | Help text |
| `error` | string | null | Error message |
| `className` | string | '' | Additional CSS classes |
| `disabled` | boolean | false | Whether the field is disabled |

## MediaHelper Methods

### Basic URL Methods

```php
use App\Services\MediaHelper;

// Get media URL by ID
$url = MediaHelper::getUrl($mediaId);

// Get thumbnail URL
$thumbnailUrl = MediaHelper::getThumbnailUrl($mediaId, 'medium');

// Get full URL
$fullUrl = MediaHelper::getFullUrl($mediaId);
```

### Media Information Methods

```php
// Get media model
$media = MediaHelper::getMedia($mediaId);

// Get media with metadata
$mediaWithMetadata = MediaHelper::getMediaWithMetadata($mediaId);

// Get alt text
$altText = MediaHelper::getAltText($mediaId);

// Get title
$title = MediaHelper::getTitle($mediaId);

// Check if user can access media
$canAccess = MediaHelper::canAccess($mediaId, $user);
```

### Utility Methods

```php
// Get dimensions
$dimensions = MediaHelper::getDimensions($mediaId);
// Returns: ['width' => 1920, 'height' => 1080] or null

// Get formatted file size
$size = MediaHelper::getFormattedSize($mediaId);

// Check file types
$isImage = MediaHelper::isImage($mediaId);
$isVideo = MediaHelper::isVideo($mediaId);

// Clear cache
MediaHelper::clearCache($mediaId);
```

### Profile Image Specific Methods

```php
// Get profile image URL with fallback
$profileUrl = MediaHelper::getProfileImageUrl($mediaId, $fallbackPath);

// Get profile thumbnail with fallback
$profileThumbnail = MediaHelper::getProfileThumbnailUrl($mediaId, $fallbackPath, 'small');
```

## Advanced Usage Examples

### Multiple Media Selection

For models that need multiple media files, you can use multiple MediaSelector components:

```jsx
// Example: Product with multiple images
const form = useForm({
    featured_image_id: product.featured_image_id,
    gallery_image_ids: product.gallery_image_ids || [],
});

return (
    <>
        <MediaSelector
            label="Featured Image"
            value={form.data.featured_image_id}
            onChange={(id) => form.setData('featured_image_id', id)}
            selectedMedia={product.featured_image}
            accept="image/*"
        />
        
        {/* You would need to create a MultipleMediaSelector component for gallery */}
    </>
);
```

### Custom File Type Filtering

```jsx
// Only allow images
<MediaSelector accept="image/*" />

// Only allow videos
<MediaSelector accept="video/*" />

// Only allow documents
<MediaSelector accept="document/*" />

// Allow all file types
<MediaSelector accept="all" />
```

### Loading Media Relationships

Make sure to load media relationships in your controllers:

```php
// In your controller
public function edit(YourModel $model)
{
    $model->load('media', 'featuredImage'); // Load relationships
    
    return Inertia::render('YourPage', [
        'model' => $model,
    ]);
}
```

## Best Practices

1. **Always use media IDs** instead of storing file paths directly
2. **Load media relationships** when passing data to frontend components
3. **Validate media IDs** in your form requests/controllers
4. **Use appropriate file type filters** to improve user experience
5. **Provide fallback handling** for legacy file paths if migrating existing data
6. **Cache media data** using the MediaHelper service for better performance
7. **Clear cache** when media files are updated or deleted

## Migration from Direct File Storage

If you're migrating from storing file paths directly:

1. Add the new media ID field alongside the existing path field
2. Create a migration script to move existing files to the media system
3. Update your forms to use MediaSelector
4. Use MediaHelper methods with fallback support
5. Gradually phase out the old path fields

## Troubleshooting

### Common Issues

1. **Media not loading in selector**: Ensure the media relationship is loaded in your controller
2. **Permission errors**: Check that the user has access to the media files
3. **Upload failures**: Verify media upload permissions and file size limits
4. **Cache issues**: Clear media cache using `MediaHelper::clearCache($mediaId)`

### Debug Tips

```php
// Check if media exists
$media = MediaHelper::getMedia($mediaId);
if (!$media) {
    // Media not found
}

// Check user permissions
$canAccess = MediaHelper::canAccess($mediaId, $user);
if (!$canAccess) {
    // User cannot access this media
}
```
