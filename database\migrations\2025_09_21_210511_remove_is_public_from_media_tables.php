<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove is_public column from media table
        Schema::table('media', function (Blueprint $table) {
            $table->dropIndex(['is_public']);
            $table->dropColumn('is_public');
        });

        // Remove is_public column from media_folders table
        Schema::table('media_folders', function (Blueprint $table) {
            $table->dropIndex(['is_public']);
            $table->dropColumn('is_public');
        });

        // Remove is_public column from media_collections table if it exists
        if (Schema::hasTable('media_collections')) {
            Schema::table('media_collections', function (Blueprint $table) {
                $table->dropIndex(['is_public']);
                $table->dropColumn('is_public');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Add back is_public column to media table
        Schema::table('media', function (Blueprint $table) {
            $table->boolean('is_public')->default(false);
            $table->index('is_public');
        });

        // Add back is_public column to media_folders table
        Schema::table('media_folders', function (Blueprint $table) {
            $table->boolean('is_public')->default(false);
            $table->index('is_public');
        });

        // Add back is_public column to media_collections table if it exists
        if (Schema::hasTable('media_collections')) {
            Schema::table('media_collections', function (Blueprint $table) {
                $table->boolean('is_public')->default(false);
                $table->index('is_public');
            });
        }
    }
};
