<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Enums\UserType;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Inertia\Response as InertiaResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;

class AgentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): InertiaResponse
    {
        $user = $request->user();

        // Get company admin's owned teams
        $ownedTeamIds = $user->ownedTeams()->pluck('id');

        if ($ownedTeamIds->isEmpty()) {
            // If no teams, show empty state
            return Inertia::render('Company/Agents/Index', [
                'agents' => new \Illuminate\Pagination\LengthAwarePaginator([], 0, 10),
                'filters' => [
                    'search' => '',
                    'per_page' => 10,
                ],
            ]);
        }

        // Get search and filter parameters
        $search = $request->get('search');
        $perPage = $request->get('per_page', 10);

        // Get agents from all owned teams
        $agents = User::select('users.*', 'team_user.role', 'team_user.created_at as joined_at', 'teams.name as team_name')
            ->join('team_user', 'users.id', '=', 'team_user.user_id')
            ->join('teams', 'team_user.team_id', '=', 'teams.id')
            ->whereIn('team_user.team_id', $ownedTeamIds)
            ->where('users.user_type', 'individual_agent')
            ->when($search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('users.name', 'like', "%{$search}%")
                      ->orWhere('users.email', 'like', "%{$search}%")
                      ->orWhere('users.license_number', 'like', "%{$search}%");
                });
            })
            ->orderBy('team_user.created_at', 'desc')
            ->paginate($perPage);

        // Transform data to include pivot-like structure for frontend compatibility
        $agents->getCollection()->transform(function ($agent) {
            $agent->pivot = (object) [
                'role' => $agent->role,
                'created_at' => $agent->joined_at
            ];
            return $agent;
        });

        return Inertia::render('Company/Agents/Index', [
            'agents' => $agents,
            'filters' => [
                'search' => $search,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): InertiaResponse
    {
        $user = $request->user();

        // Get or create a default team for the company admin
        $team = $user->ownedTeams()->first();

        if (!$team) {
            // Create a default team for the company admin
            $teamName = $user->company_name ?: ($user->name . "'s Company");
            $team = $user->ownedTeams()->create([
                'name' => $teamName,
                'personal_team' => false,
                'team_type' => 'agency',
            ]);
        }

        // Available roles for agents
        $availableRoles = [
            ['value' => 'agent', 'label' => 'Agent'],
            ['value' => 'senior_agent', 'label' => 'Senior Agent'],
            ['value' => 'team_lead', 'label' => 'Team Lead'],
        ];

        return Inertia::render('Company/Agents/Create', [
            'team' => $team,
            'availableRoles' => $availableRoles,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Get or create a default team for the company admin
        $team = $user->ownedTeams()->first();

        if (!$team) {
            // Create a default team for the company admin
            $teamName = $user->company_name ?: ($user->name . "'s Company");
            $team = $user->ownedTeams()->create([
                'name' => $teamName,
                'personal_team' => false,
                'team_type' => 'agency',
            ]);
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'phone' => ['nullable', 'string', 'max:20'],
            'license_number' => ['nullable', 'string', 'max:50'],
            'license_state' => ['nullable', 'string', 'max:2'],
            'license_expiry' => ['nullable', 'date'],
            'role' => ['required', 'string', 'in:agent,senior_agent,team_lead'],
        ]);

        // Create the agent user
        $agent = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'phone' => $validated['phone'],
            'user_type' => UserType::COMPANY_AGENT,
            'license_number' => $validated['license_number'],
            'license_state' => $validated['license_state'],
            'license_expiry' => $validated['license_expiry'],
            'email_verified_at' => now(), // Auto-verify company-created agents
        ]);

        // Add agent to team with specified role
        $team->users()->attach($agent, ['role' => $validated['role']]);

        // Set the team as agent's current team
        $agent->current_team_id = $team->id;
        $agent->save();

        return redirect()->route('company.agents.index')
            ->with('success', 'Agent created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, User $agent): InertiaResponse
    {
        $user = $request->user();
        $ownedTeamIds = $user->ownedTeams()->pluck('id');

        // Check if agent belongs to any of the company admin's teams
        $teamUser = DB::table('team_user')
            ->join('teams', 'team_user.team_id', '=', 'teams.id')
            ->where('team_user.user_id', $agent->id)
            ->whereIn('team_user.team_id', $ownedTeamIds)
            ->select('team_user.*', 'teams.name as team_name')
            ->first();

        if (!$teamUser) {
            abort(404, 'Agent not found in your company');
        }

        // Add team info to agent
        $agent->team_role = $teamUser->role;
        $agent->team_joined_at = $teamUser->created_at;
        $agent->team_name = $teamUser->team_name;

        return Inertia::render('Company/Agents/Show', [
            'agent' => $agent,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, User $agent): InertiaResponse
    {
        $user = $request->user();
        $ownedTeamIds = $user->ownedTeams()->pluck('id');

        // Check if agent belongs to any of the company admin's teams
        $teamUser = DB::table('team_user')
            ->join('teams', 'team_user.team_id', '=', 'teams.id')
            ->where('team_user.user_id', $agent->id)
            ->whereIn('team_user.team_id', $ownedTeamIds)
            ->select('team_user.*', 'teams.name as team_name')
            ->first();

        if (!$teamUser) {
            abort(404, 'Agent not found in your company');
        }

        // Available roles for agents
        $availableRoles = [
            ['value' => 'agent', 'label' => 'Agent'],
            ['value' => 'senior_agent', 'label' => 'Senior Agent'],
            ['value' => 'team_lead', 'label' => 'Team Lead'],
        ];

        return Inertia::render('Company/Agents/Edit', [
            'agent' => $agent,
            'team' => (object) ['name' => $teamUser->team_name],
            'availableRoles' => $availableRoles,
            'currentRole' => $teamUser->role,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $agent): RedirectResponse
    {
        $team = $request->user()->currentTeam;

        if (!$team || !$team->hasUser($agent)) {
            abort(404, 'Agent not found in your team');
        }

        // Authorize team management
        Gate::authorize('updateTeamMember', $team);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $agent->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'license_number' => ['nullable', 'string', 'max:50'],
            'license_state' => ['nullable', 'string', 'max:2'],
            'license_expiry' => ['nullable', 'date'],
            'role' => ['required', 'string', 'in:agent,senior_agent,team_lead'],
        ]);

        // Update agent details
        $agent->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'license_number' => $validated['license_number'],
            'license_state' => $validated['license_state'],
            'license_expiry' => $validated['license_expiry'],
        ]);

        // Update team role
        $team->users()->updateExistingPivot($agent->id, ['role' => $validated['role']]);

        return redirect()->route('company.agents.index')
            ->with('success', 'Agent updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, User $agent): RedirectResponse
    {
        $team = $request->user()->currentTeam;

        if (!$team || !$team->hasUser($agent)) {
            abort(404, 'Agent not found in your team');
        }

        // Authorize team management
        Gate::authorize('removeTeamMember', $team);

        // Remove agent from team
        $team->users()->detach($agent);

        return redirect()->route('company.agents.index')
            ->with('success', 'Agent removed from team successfully.');
    }
}
