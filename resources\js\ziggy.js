const Ziggy = {"url":"http:\/\/localhost:8010","port":8010,"defaults":{},"routes":{"filament.exports.download":{"uri":"filament\/exports\/{export}\/download","methods":["GET","HEAD"],"parameters":["export"],"bindings":{"export":"id"}},"filament.imports.failed-rows.download":{"uri":"filament\/imports\/{import}\/failed-rows\/download","methods":["GET","HEAD"],"parameters":["import"],"bindings":{"import":"id"}},"filament.admin.auth.login":{"uri":"admin\/login","methods":["GET","HEAD"]},"filament.admin.auth.logout":{"uri":"admin\/logout","methods":["POST"]},"filament.admin.pages.dashboard":{"uri":"admin","methods":["GET","HEAD"]},"admin.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"scribe":{"uri":"docs","methods":["GET","HEAD"]},"scribe.postman":{"uri":"docs.postman","methods":["GET","HEAD"]},"scribe.openapi":{"uri":"docs.openapi","methods":["GET","HEAD"]},"cashier.payment":{"uri":"stripe\/payment\/{id}","methods":["GET","HEAD"],"parameters":["id"]},"cashier.webhook":{"uri":"stripe\/webhook","methods":["POST"]},"login":{"uri":"login","methods":["GET","HEAD"]},"login.store":{"uri":"login","methods":["POST"]},"logout":{"uri":"logout","methods":["POST"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.update":{"uri":"reset-password","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"register.store":{"uri":"register","methods":["POST"]},"verification.notice":{"uri":"email\/verify","methods":["GET","HEAD"]},"verification.verify":{"uri":"email\/verify\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"user-profile-information.update":{"uri":"user\/profile-information","methods":["PUT"]},"user-password.update":{"uri":"user\/password","methods":["PUT"]},"password.confirm":{"uri":"user\/confirm-password","methods":["GET","HEAD"]},"password.confirmation":{"uri":"user\/confirmed-password-status","methods":["GET","HEAD"]},"password.confirm.store":{"uri":"user\/confirm-password","methods":["POST"]},"two-factor.login":{"uri":"two-factor-challenge","methods":["GET","HEAD"]},"two-factor.login.store":{"uri":"two-factor-challenge","methods":["POST"]},"two-factor.enable":{"uri":"user\/two-factor-authentication","methods":["POST"]},"two-factor.confirm":{"uri":"user\/confirmed-two-factor-authentication","methods":["POST"]},"two-factor.disable":{"uri":"user\/two-factor-authentication","methods":["DELETE"]},"two-factor.qr-code":{"uri":"user\/two-factor-qr-code","methods":["GET","HEAD"]},"two-factor.secret-key":{"uri":"user\/two-factor-secret-key","methods":["GET","HEAD"]},"two-factor.recovery-codes":{"uri":"user\/two-factor-recovery-codes","methods":["GET","HEAD"]},"terms.show":{"uri":"terms-of-service","methods":["GET","HEAD"]},"policy.show":{"uri":"privacy-policy","methods":["GET","HEAD"]},"profile.show":{"uri":"user\/profile","methods":["GET","HEAD"]},"other-browser-sessions.destroy":{"uri":"user\/other-browser-sessions","methods":["DELETE"]},"current-user-photo.destroy":{"uri":"user\/profile-photo","methods":["DELETE"]},"current-user.destroy":{"uri":"user","methods":["DELETE"]},"api-tokens.index":{"uri":"user\/api-tokens","methods":["GET","HEAD"]},"api-tokens.store":{"uri":"user\/api-tokens","methods":["POST"]},"api-tokens.update":{"uri":"user\/api-tokens\/{token}","methods":["PUT"],"parameters":["token"]},"api-tokens.destroy":{"uri":"user\/api-tokens\/{token}","methods":["DELETE"],"parameters":["token"]},"teams.create":{"uri":"teams\/create","methods":["GET","HEAD"]},"teams.store":{"uri":"teams","methods":["POST"]},"teams.show":{"uri":"teams\/{team}","methods":["GET","HEAD"],"parameters":["team"]},"teams.update":{"uri":"teams\/{team}","methods":["PUT"],"parameters":["team"]},"teams.destroy":{"uri":"teams\/{team}","methods":["DELETE"],"parameters":["team"]},"current-team.update":{"uri":"current-team","methods":["PUT"]},"team-members.store":{"uri":"teams\/{team}\/members","methods":["POST"],"parameters":["team"]},"team-members.update":{"uri":"teams\/{team}\/members\/{user}","methods":["PUT"],"parameters":["team","user"]},"team-members.destroy":{"uri":"teams\/{team}\/members\/{user}","methods":["DELETE"],"parameters":["team","user"]},"team-invitations.accept":{"uri":"team-invitations\/{invitation}","methods":["GET","HEAD"],"parameters":["invitation"]},"team-invitations.destroy":{"uri":"team-invitations\/{invitation}","methods":["DELETE"],"parameters":["invitation"]},"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"livewire.update":{"uri":"livewire\/update","methods":["POST"]},"livewire.upload-file":{"uri":"livewire\/upload-file","methods":["POST"]},"livewire.preview-file":{"uri":"livewire\/preview-file\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"user.index":{"uri":"api\/user","methods":["GET","HEAD"]},"user.store":{"uri":"api\/user","methods":["POST"]},"user.show":{"uri":"api\/user\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"user.update":{"uri":"api\/user\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"user.destroy":{"uri":"api\/user\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"home":{"uri":"\/","methods":["GET","HEAD"]},"oauth.redirect":{"uri":"auth\/redirect\/{provider}","methods":["GET","HEAD"],"parameters":["provider"]},"oauth.callback":{"uri":"auth\/callback\/{provider}","methods":["GET","HEAD"],"parameters":["provider"]},"login-link.store":{"uri":"auth\/login-link","methods":["POST"]},"login-link.login":{"uri":"auth\/login-link\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"oauth.destroy":{"uri":"auth\/destroy\/{provider}","methods":["DELETE"],"parameters":["provider"]},"chat.index":{"uri":"chat","methods":["GET","HEAD"]},"subscriptions.index":{"uri":"subscriptions","methods":["GET","HEAD"]},"subscriptions.create":{"uri":"subscriptions\/create","methods":["GET","HEAD"]},"subscriptions.store":{"uri":"subscriptions","methods":["POST"]},"subscriptions.show":{"uri":"subscriptions\/{subscription}","methods":["GET","HEAD"],"parameters":["subscription"]},"admin.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"admin.users.store":{"uri":"admin\/users","methods":["POST"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT","PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"],"bindings":{"user":"id"}},"admin.plans.index":{"uri":"admin\/plans","methods":["GET","HEAD"]},"admin.plans.create":{"uri":"admin\/plans\/create","methods":["GET","HEAD"]},"admin.plans.store":{"uri":"admin\/plans","methods":["POST"]},"admin.plans.show":{"uri":"admin\/plans\/{plan}","methods":["GET","HEAD"],"parameters":["plan"],"bindings":{"plan":"id"}},"admin.plans.edit":{"uri":"admin\/plans\/{plan}\/edit","methods":["GET","HEAD"],"parameters":["plan"],"bindings":{"plan":"id"}},"admin.plans.update":{"uri":"admin\/plans\/{plan}","methods":["PUT","PATCH"],"parameters":["plan"],"bindings":{"plan":"id"}},"admin.plans.destroy":{"uri":"admin\/plans\/{plan}","methods":["DELETE"],"parameters":["plan"],"bindings":{"plan":"id"}},"admin.users.api":{"uri":"admin\/users\/api\/data","methods":["GET","HEAD"]},"admin.plans.api":{"uri":"admin\/plans\/api\/data","methods":["GET","HEAD"]},"company.agents.index":{"uri":"company\/agents","methods":["GET","HEAD"]},"company.agents.create":{"uri":"company\/agents\/create","methods":["GET","HEAD"]},"company.agents.store":{"uri":"company\/agents","methods":["POST"]},"company.agents.show":{"uri":"company\/agents\/{agent}","methods":["GET","HEAD"],"parameters":["agent"],"bindings":{"agent":"id"}},"company.agents.edit":{"uri":"company\/agents\/{agent}\/edit","methods":["GET","HEAD"],"parameters":["agent"],"bindings":{"agent":"id"}},"company.agents.update":{"uri":"company\/agents\/{agent}","methods":["PUT","PATCH"],"parameters":["agent"],"bindings":{"agent":"id"}},"company.agents.destroy":{"uri":"company\/agents\/{agent}","methods":["DELETE"],"parameters":["agent"],"bindings":{"agent":"id"}},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
