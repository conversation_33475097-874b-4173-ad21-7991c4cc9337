import React, { useState } from 'react';
import { router } from '@inertiajs/react';
import axios from 'axios';
import { useToast } from '@/Components/hooks/use-toast';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/shadcn/ui/card';
import { Badge } from '@/Components/shadcn/ui/badge';

import {
    Folder,
    FolderOpen,
    File,
    Image,
    Video,
    Music,
    FileText,
    Archive,
    Download,
    Edit,
    Trash2,
    Move,
    Copy
} from 'lucide-react';
import {
    ContextMenu,
    ContextMenuContent,
    ContextMenuItem,
    ContextMenuSeparator,
    ContextMenuTrigger,
} from '@/Components/shadcn/ui/context-menu';
import { ConfirmDialog } from '@/Components/shadcn/ui/confirm-dialog';

export default function FileManager({
    folders = [],
    media = [],
    onMediaClick,
    onMediaEdit,
    onFolderEdit,
    onMediaMove,
    onFolderMove,
    onCopyUrl
}) {
    const { toast } = useToast();

    // State for confirm dialogs
    const [deleteConfirm, setDeleteConfirm] = useState({
        isOpen: false,
        type: null, // 'media' or 'folder'
        item: null
    });

    // Delete handlers
    const handleDeleteMedia = (mediaItem) => {
        setDeleteConfirm({
            isOpen: true,
            type: 'media',
            item: mediaItem
        });
    };

    const handleDeleteFolder = (folder) => {
        setDeleteConfirm({
            isOpen: true,
            type: 'folder',
            item: folder
        });
    };

    const confirmDelete = async () => {
        const { type, item } = deleteConfirm;

        try {
            const isCompanyContext = window.location.pathname.includes('/company/');

            if (type === 'media') {
                const routeName = isCompanyContext ? 'company.media.destroy' : 'admin.media.destroy';
                await axios.delete(route(routeName, item.id));

                toast({
                    title: "Success",
                    description: "File deleted successfully",
                });
            } else if (type === 'folder') {
                const routeName = isCompanyContext ? 'company.media-folders.destroy' : 'admin.media-folders.destroy';
                await axios.delete(route(routeName, item.id));

                toast({
                    title: "Success",
                    description: "Folder deleted successfully",
                });
            }

            // Refresh the page
            router.reload();
        } catch (error) {
            console.error('Delete error:', error);

            let errorMessage = error.response?.data?.message || `Failed to delete ${type}`;

            // Add debug details if available
            if (error.response?.data?.error_details) {
                console.error('Error details:', error.response.data.error_details);
            }

            toast({
                title: "Error",
                description: errorMessage,
                variant: "destructive",
            });
        }
    };

    // Get file icon based on type
    const getFileIcon = (mediaItem) => {
        const type = mediaItem.mime_type?.split('/')[0];
        const extension = mediaItem.extension?.toLowerCase();

        switch (type) {
            case 'image':
                return <Image className="w-8 h-8 text-blue-500" />;
            case 'video':
                return <Video className="w-8 h-8 text-purple-500" />;
            case 'audio':
                return <Music className="w-8 h-8 text-green-500" />;
            default:
                if (['pdf', 'doc', 'docx', 'txt'].includes(extension)) {
                    return <FileText className="w-8 h-8 text-red-500" />;
                }
                if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
                    return <Archive className="w-8 h-8 text-orange-500" />;
                }
                return <File className="w-8 h-8 text-gray-500" />;
        }
    };

    // Format file size
    const formatFileSize = (bytes) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Handle folder click
    const handleFolderClick = (folder) => {
        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        router.get(route(routeName), { folder_id: folder.id });
    };



    return (
        <>
        <div className="space-y-4">
            {/* Toolbar */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                        {(folders?.length || 0) + (media.data?.length || 0)} items
                    </span>
                </div>
            </div>

            {/* Grid */}
            <div className="grid gap-4" style={{ gridTemplateColumns: 'repeat(auto-fill, 150px)' }}>
                {/* Folders */}
                {folders.map((folder) => (
                    <ContextMenu key={`folder-${folder.id}`}>
                        <ContextMenuTrigger>
                            <Card
                                className="cursor-pointer transition-all hover:shadow-md rounded-lg overflow-hidden"
                                style={{ width: '150px', height: '150px' }}
                            >
                                <CardContent className="p-0 h-full">
                                    <div
                                        className="flex flex-col h-full"
                                        onClick={() => handleFolderClick(folder)}
                                    >
                                        {/* Folder Icon Area - 120px height */}
                                        <div className="flex items-center justify-center bg-muted relative" style={{ height: '120px' }}>
                                            <Folder className="w-16 h-16 text-blue-500" />
                                            {folder.media_count > 0 && (
                                                <Badge
                                                    variant="secondary"
                                                    className="absolute top-2 right-2 text-xs px-1"
                                                >
                                                    {folder.media_count}
                                                </Badge>
                                            )}
                                        </div>

                                        {/* Folder Name Area - 30px height */}
                                        <div className="p-2 flex-1 flex flex-col justify-center">
                                            <p className="text-xs font-medium truncate text-center" title={folder.name}>
                                                {folder.name}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </ContextMenuTrigger>
                        <ContextMenuContent>
                            <ContextMenuItem onClick={() => onFolderEdit && onFolderEdit(folder)}>
                                <Edit className="w-4 h-4 mr-2" />
                                Rename
                            </ContextMenuItem>
                            <ContextMenuItem onClick={() => onFolderMove && onFolderMove(folder)}>
                                <Move className="w-4 h-4 mr-2" />
                                Move
                            </ContextMenuItem>
                            <ContextMenuSeparator />
                            <ContextMenuItem
                                className="text-destructive"
                                onClick={() => handleDeleteFolder(folder)}
                            >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                            </ContextMenuItem>
                        </ContextMenuContent>
                    </ContextMenu>
                ))}

                {/* Files */}
                {media.data?.map((mediaItem) => (
                    <ContextMenu key={`media-${mediaItem.id}`}>
                        <ContextMenuTrigger>
                            <Card
                                className="cursor-pointer transition-all hover:shadow-md rounded-lg overflow-hidden"
                                style={{ width: '150px', height: '150px' }}
                            >
                                <CardContent className="p-0 h-full">
                                    <div
                                        className="flex flex-col h-full"
                                        onClick={() => onMediaClick && onMediaClick(mediaItem)}
                                    >
                                        {/* File Preview/Icon Area - 120px height */}
                                        <div className="relative" style={{ height: '120px' }}>
                                            {mediaItem.mime_type?.startsWith('image/') ? (
                                                <img
                                                    src={mediaItem.thumbnail_url || mediaItem.url}
                                                    alt={mediaItem.alt_text || mediaItem.original_filename}
                                                    className="w-full h-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full flex items-center justify-center bg-muted">
                                                    {getFileIcon(mediaItem)}
                                                </div>
                                            )}
                                        </div>

                                        {/* File Name Area - 30px height */}
                                        <div className="p-2 flex-1 flex flex-col justify-center">
                                            <p className="text-xs font-medium truncate text-center" title={mediaItem.original_filename}>
                                                {mediaItem.original_filename}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </ContextMenuTrigger>
                        <ContextMenuContent>
                            <ContextMenuItem onClick={() => window.open(mediaItem.url, '_blank')}>
                                <Download className="w-4 h-4 mr-2" />
                                Download
                            </ContextMenuItem>
                            <ContextMenuItem onClick={() => onMediaEdit && onMediaEdit(mediaItem)}>
                                <Edit className="w-4 h-4 mr-2" />
                                Edit
                            </ContextMenuItem>
                            <ContextMenuItem onClick={() => onCopyUrl && onCopyUrl(mediaItem.url)}>
                                <Copy className="w-4 h-4 mr-2" />
                                Copy URL
                            </ContextMenuItem>
                            <ContextMenuItem onClick={() => onMediaMove && onMediaMove(mediaItem)}>
                                <Move className="w-4 h-4 mr-2" />
                                Move
                            </ContextMenuItem>
                            <ContextMenuSeparator />
                            <ContextMenuItem
                                className="text-destructive"
                                onClick={() => handleDeleteMedia(mediaItem)}
                            >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                            </ContextMenuItem>
                        </ContextMenuContent>
                    </ContextMenu>
                ))}
            </div>

            {/* Empty State */}
            {(folders?.length || 0) + (media.data?.length || 0) === 0 && (
                <div className="text-center py-12">
                    <Folder className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-muted-foreground mb-2">
                        This folder is empty
                    </h3>
                    <p className="text-sm text-muted-foreground">
                        Upload files or create folders to get started
                    </p>
                </div>
            )}
        </div>

        {/* Confirm Delete Dialog */}
        <ConfirmDialog
            isOpen={deleteConfirm.isOpen}
            onClose={() => setDeleteConfirm({ isOpen: false, type: null, item: null })}
            onConfirm={confirmDelete}
            title={`Delete ${deleteConfirm.type === 'folder' ? 'Folder' : 'File'}?`}
            description={
                deleteConfirm.type === 'folder'
                    ? `Are you sure you want to delete "${deleteConfirm.item?.name}"? All files in this folder will removed as well.`
                    : `Are you sure you want to delete "${deleteConfirm.item?.original_filename}"? This action cannot be undone.`
            }
            confirmText="Delete"
            cancelText="Cancel"
            variant="destructive"
        />
        </>
    );
}
