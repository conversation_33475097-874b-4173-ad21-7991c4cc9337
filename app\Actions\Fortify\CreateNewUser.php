<?php

declare(strict_types=1);

namespace App\Actions\Fortify;

use App\Enums\UserType;
use App\Enums\TeamType;
use App\Models\Team;
use App\Models\User;
use Stripe\Customer;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Laravel\Jetstream\Jetstream;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\CreatesNewUsers;

final class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Create a newly registered user.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {
        $this->validateInput($input);

        return DB::transaction(fn () => tap($this->createUser($input), function (User $user) use ($input): void {
            $this->createTeam($user, $input);
            $this->createCustomer($user);
        }));
    }

    /**
     * Validate the registration input.
     *
     * @param  array<string, mixed>  $input
     */
    private function validateInput(array $input): void
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => Arr::get($input, 'password') ? $this->passwordRules() : 'sometimes',
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['accepted', 'required'] : '',
            'user_type' => ['required', 'string', 'in:independent_agent,brokerage_admin'],
        ];

        // Add company-specific validation for brokerage admins
        if (Arr::get($input, 'user_type') === UserType::BROKERAGE_ADMIN->value) {
            $rules = array_merge($rules, [
                'company_name' => ['required', 'string', 'max:255'],
                'company_address' => ['nullable', 'string', 'max:500'],
                'company_website' => ['nullable', 'url', 'max:255'],
                'team_type' => ['required', 'string', 'in:' . implode(',', array_column(TeamType::cases(), 'value'))],
            ]);
        }

        // Add optional profile fields
        $rules = array_merge($rules, [
            'phone' => ['nullable', 'string', 'max:20'],
            'license_number' => ['nullable', 'string', 'max:50'],
            'license_state' => ['nullable', 'string', 'max:2'],
        ]);

        Validator::make($input, $rules)->validate();
    }

    /**
     * Create the user with the provided input.
     *
     * @param  array<string, mixed>  $input
     */
    private function createUser(array $input): User
    {
        $userData = [
            'name' => $input['name'],
            'email' => $input['email'],
            'password' => Arr::get($input, 'password') ? Hash::make($input['password']) : Str::random(12),
            'user_type' => UserType::from($input['user_type']),
        ];

        // Add optional profile fields if provided
        if (Arr::get($input, 'phone')) {
            $userData['phone'] = $input['phone'];
        }

        if (Arr::get($input, 'license_number')) {
            $userData['license_number'] = $input['license_number'];
        }

        if (Arr::get($input, 'license_state')) {
            $userData['license_state'] = $input['license_state'];
        }

        // Add company-specific fields for brokerage admins
        if ($userData['user_type'] === UserType::BROKERAGE_ADMIN) {
            $userData = array_merge($userData, [
                'company_name' => $input['company_name'],
                'company_address' => Arr::get($input, 'company_address'),
                'company_website' => Arr::get($input, 'company_website'),
            ]);
        }

        return User::query()->create($userData);
    }

    /**
     * Create a team for the user based on their type.
     *
     * @param  array<string, mixed>  $input
     */
    private function createTeam(User $user, array $input): void
    {
        // Independent agents get a personal team
        if ($user->user_type === UserType::INDEPENDENT_AGENT) {
            $user->ownedTeams()->save(Team::query()->forceCreate([
                'user_id' => $user->id,
                'name' => explode(' ', $user->name, 2)[0]."'s Team",
                'personal_team' => true,
                'team_type' => TeamType::TEAM,
            ]));
            return;
        }

        // Brokerage admins get a company/agency team
        if ($user->user_type === UserType::BROKERAGE_ADMIN) {
            $teamType = TeamType::from($input['team_type']);
            $teamName = $user->company_name ?? explode(' ', $user->name, 2)[0]."'s Company";

            $teamData = [
                'user_id' => $user->id,
                'name' => $teamName,
                'personal_team' => false,
                'team_type' => $teamType,
                'max_agents' => $teamType->defaultMaxAgents(),
            ];

            // Add company details to team if provided
            if (Arr::get($input, 'company_address')) {
                $teamData['address'] = $input['company_address'];
            }

            if (Arr::get($input, 'company_website')) {
                $teamData['website'] = $input['company_website'];
            }

            $user->ownedTeams()->save(Team::query()->forceCreate($teamData));
            return;
        }

        // Note: Team agents will be created through company dashboard
        // They don't register directly
    }

    /**
     * Create a billing customer for the user.
     */
    private function createCustomer(User $user): void
    {
        if (! Config::get('cashier.billing_enabled')) {
            return;
        }

        /** @var Customer $stripeCustomer */
        $stripeCustomer = $user->createOrGetStripeCustomer();

        $user->update([
            'stripe_id' => $stripeCustomer->id,
        ]);
    }
}
