[2025-09-22 11:44:05] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at D:\\laragon\\www\\mls\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#7 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-09-22 11:52:52] local.INFO: Media upload request received {"files_count":1,"user_id":1,"request_data":[]} 
[2025-09-22 11:52:52] local.INFO: Processing files for upload {"files_count":1,"user_id":1} 
[2025-09-22 11:52:52] local.INFO: Processing individual file {"filename":"download.jpg","size":16662,"mime_type":"image/jpeg"} 
[2025-09-22 11:52:58] local.INFO: Generated thumb thumbnail for media 1 {"media_id":1,"size":"thumb","path":"media/ac42c2d9-7803-4351-9d10-bcdd5ac246a7-150x150.jpg","dimensions":"150x150"} 
[2025-09-22 11:52:58] local.INFO: Generated small thumbnail for media 1 {"media_id":1,"size":"small","path":"media/ac42c2d9-7803-4351-9d10-bcdd5ac246a7-300x300.jpg","dimensions":"300x300"} 
[2025-09-22 11:52:58] local.INFO: Generated medium thumbnail for media 1 {"media_id":1,"size":"medium","path":"media/ac42c2d9-7803-4351-9d10-bcdd5ac246a7-600x600.jpg","dimensions":"600x600"} 
[2025-09-22 11:52:58] local.INFO: Generated large thumbnail for media 1 {"media_id":1,"size":"large","path":"media/ac42c2d9-7803-4351-9d10-bcdd5ac246a7-1200x1200.jpg","dimensions":"1200x1200"} 
[2025-09-22 11:52:58] local.INFO: Thumbnails generated for media {"media_id":1,"filename":"download.jpg"} 
[2025-09-22 11:52:58] local.INFO: File uploaded successfully {"media_id":1,"filename":"download.jpg"} 
[2025-09-22 11:52:58] local.INFO: Upload process completed {"uploaded_count":1,"errors_count":0} 
[2025-09-22 12:10:45] local.INFO: Media upload request received {"files_count":4,"user_id":1,"request_data":[]} 
[2025-09-22 12:10:45] local.INFO: Processing files for upload {"files_count":4,"user_id":1} 
[2025-09-22 12:10:45] local.INFO: Processing individual file {"filename":"cd3dca802b433e25576d.svg","size":10298,"mime_type":"image/svg+xml"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":2,"size":"thumb","original_path":"media/1f21f4bd-e96b-4625-8061-13f863207766.svg","thumbnail_path":"media/1f21f4bd-e96b-4625-8061-13f863207766-150x150.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":2,"size":"small","original_path":"media/1f21f4bd-e96b-4625-8061-13f863207766.svg","thumbnail_path":"media/1f21f4bd-e96b-4625-8061-13f863207766-300x300.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":2,"size":"medium","original_path":"media/1f21f4bd-e96b-4625-8061-13f863207766.svg","thumbnail_path":"media/1f21f4bd-e96b-4625-8061-13f863207766-600x600.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":2,"size":"large","original_path":"media/1f21f4bd-e96b-4625-8061-13f863207766.svg","thumbnail_path":"media/1f21f4bd-e96b-4625-8061-13f863207766-1200x1200.svg"} 
[2025-09-22 12:10:45] local.INFO: Thumbnails generated for media {"media_id":2,"filename":"cd3dca802b433e25576d.svg"} 
[2025-09-22 12:10:45] local.INFO: File uploaded successfully {"media_id":2,"filename":"cd3dca802b433e25576d.svg"} 
[2025-09-22 12:10:45] local.INFO: Processing individual file {"filename":"paypal-color.svg","size":3438,"mime_type":"image/svg+xml"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":3,"size":"thumb","original_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f.svg","thumbnail_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f-150x150.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":3,"size":"small","original_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f.svg","thumbnail_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f-300x300.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":3,"size":"medium","original_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f.svg","thumbnail_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f-600x600.svg"} 
[2025-09-22 12:10:45] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":3,"size":"large","original_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f.svg","thumbnail_path":"media/b3f15360-ae29-43d3-bbfa-7e46bf77994f-1200x1200.svg"} 
[2025-09-22 12:10:45] local.INFO: Thumbnails generated for media {"media_id":3,"filename":"paypal-color.svg"} 
[2025-09-22 12:10:45] local.INFO: File uploaded successfully {"media_id":3,"filename":"paypal-color.svg"} 
[2025-09-22 12:10:45] local.INFO: Processing individual file {"filename":"android-chrome-192x192.png","size":9500,"mime_type":"image/png"} 
[2025-09-22 12:10:45] local.INFO: Generated thumb thumbnail for media 4 {"media_id":4,"size":"thumb","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-150x150.png","dimensions":"150x150"} 
[2025-09-22 12:10:45] local.INFO: Generated small thumbnail for media 4 {"media_id":4,"size":"small","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-300x300.png","dimensions":"300x300"} 
[2025-09-22 12:10:46] local.INFO: Generated medium thumbnail for media 4 {"media_id":4,"size":"medium","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-600x600.png","dimensions":"600x600"} 
[2025-09-22 12:10:46] local.INFO: Generated large thumbnail for media 4 {"media_id":4,"size":"large","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-1200x1200.png","dimensions":"1200x1200"} 
[2025-09-22 12:10:46] local.INFO: Thumbnails generated for media {"media_id":4,"filename":"android-chrome-192x192.png"} 
[2025-09-22 12:10:46] local.INFO: File uploaded successfully {"media_id":4,"filename":"android-chrome-192x192.png"} 
[2025-09-22 12:10:46] local.INFO: Processing individual file {"filename":"email_validation_intro.8666db15.webp","size":74126,"mime_type":"image/png"} 
[2025-09-22 12:10:46] local.INFO: Generated thumb thumbnail for media 5 {"media_id":5,"size":"thumb","path":"media/7788c653-29ab-495c-8b3e-e7a231ffc64d-150x150.webp","dimensions":"150x150"} 
[2025-09-22 12:10:46] local.INFO: Generated small thumbnail for media 5 {"media_id":5,"size":"small","path":"media/7788c653-29ab-495c-8b3e-e7a231ffc64d-300x300.webp","dimensions":"300x300"} 
[2025-09-22 12:10:46] local.INFO: Generated medium thumbnail for media 5 {"media_id":5,"size":"medium","path":"media/7788c653-29ab-495c-8b3e-e7a231ffc64d-600x600.webp","dimensions":"600x600"} 
[2025-09-22 12:10:47] local.INFO: Generated large thumbnail for media 5 {"media_id":5,"size":"large","path":"media/7788c653-29ab-495c-8b3e-e7a231ffc64d-1200x1200.webp","dimensions":"1200x1200"} 
[2025-09-22 12:10:47] local.INFO: Thumbnails generated for media {"media_id":5,"filename":"email_validation_intro.8666db15.webp"} 
[2025-09-22 12:10:47] local.INFO: File uploaded successfully {"media_id":5,"filename":"email_validation_intro.8666db15.webp"} 
[2025-09-22 12:10:47] local.INFO: Upload process completed {"uploaded_count":4,"errors_count":0} 
[2025-09-22 12:46:10] local.INFO: Media upload request received {"files_count":4,"user_id":1,"request_data":{"folder_id":"2"}} 
[2025-09-22 12:46:10] local.INFO: Processing files for upload {"files_count":4,"user_id":1} 
[2025-09-22 12:46:10] local.INFO: Processing individual file {"filename":"download.jpg","size":16662,"mime_type":"image/jpeg"} 
[2025-09-22 12:46:11] local.INFO: Generated thumb thumbnail for media 6 {"media_id":6,"size":"thumb","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-150x150.jpg","dimensions":"150x150"} 
[2025-09-22 12:46:11] local.INFO: Generated small thumbnail for media 6 {"media_id":6,"size":"small","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-300x300.jpg","dimensions":"300x300"} 
[2025-09-22 12:46:11] local.INFO: Generated medium thumbnail for media 6 {"media_id":6,"size":"medium","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-600x600.jpg","dimensions":"600x600"} 
[2025-09-22 12:46:11] local.INFO: Generated large thumbnail for media 6 {"media_id":6,"size":"large","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-1200x1200.jpg","dimensions":"1200x1200"} 
[2025-09-22 12:46:11] local.INFO: Thumbnails generated for media {"media_id":6,"filename":"download.jpg"} 
[2025-09-22 12:46:11] local.INFO: File uploaded successfully {"media_id":6,"filename":"download.jpg"} 
[2025-09-22 12:46:11] local.INFO: Processing individual file {"filename":"android-chrome-192x192.png","size":9500,"mime_type":"image/png"} 
[2025-09-22 12:46:11] local.INFO: Generated thumb thumbnail for media 7 {"media_id":7,"size":"thumb","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-150x150.png","dimensions":"150x150"} 
[2025-09-22 12:46:11] local.INFO: Generated small thumbnail for media 7 {"media_id":7,"size":"small","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-300x300.png","dimensions":"300x300"} 
[2025-09-22 12:46:11] local.INFO: Generated medium thumbnail for media 7 {"media_id":7,"size":"medium","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-600x600.png","dimensions":"600x600"} 
[2025-09-22 12:46:12] local.INFO: Generated large thumbnail for media 7 {"media_id":7,"size":"large","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-1200x1200.png","dimensions":"1200x1200"} 
[2025-09-22 12:46:12] local.INFO: Thumbnails generated for media {"media_id":7,"filename":"android-chrome-192x192.png"} 
[2025-09-22 12:46:12] local.INFO: File uploaded successfully {"media_id":7,"filename":"android-chrome-192x192.png"} 
[2025-09-22 12:46:12] local.INFO: Processing individual file {"filename":"logo_3500.gif","size":3498492,"mime_type":"image/gif"} 
[2025-09-22 12:46:22] local.ERROR: Allowed memory size of 536870912 bytes exhausted (tried to allocate 4096 bytes) {"userId":1,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Allowed memory size of 536870912 bytes exhausted (tried to allocate 4096 bytes) at D:\\laragon\\www\\mls\\vendor\\intervention\\gif\\src\\Splitter.php:162)
[stacktrace]
#0 {main}
"} 
[2025-09-22 12:54:49] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at D:\\laragon\\www\\mls\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#7 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-09-22 12:57:01] local.INFO: Media upload request received {"files_count":1,"user_id":1,"request_data":{"folder_id":"1"}} 
[2025-09-22 12:57:01] local.INFO: Processing files for upload {"files_count":1,"user_id":1} 
[2025-09-22 12:57:01] local.INFO: Processing individual file {"filename":"cd3dca802b433e25576d.svg","size":10298,"mime_type":"image/svg+xml"} 
[2025-09-22 12:57:01] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":9,"size":"thumb","original_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497.svg","thumbnail_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497-150x150.svg"} 
[2025-09-22 12:57:01] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":9,"size":"small","original_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497.svg","thumbnail_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497-300x300.svg"} 
[2025-09-22 12:57:01] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":9,"size":"medium","original_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497.svg","thumbnail_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497-600x600.svg"} 
[2025-09-22 12:57:01] local.ERROR: Failed to generate thumbnail {"error":"Unable to decode input","media_id":9,"size":"large","original_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497.svg","thumbnail_path":"media/test/cfbb2301-9f75-4abd-b26d-5c34d1999497-1200x1200.svg"} 
[2025-09-22 12:57:01] local.INFO: Thumbnails generated for media {"media_id":9,"filename":"cd3dca802b433e25576d.svg"} 
[2025-09-22 12:57:01] local.INFO: File uploaded successfully {"media_id":9,"filename":"cd3dca802b433e25576d.svg"} 
[2025-09-22 12:57:01] local.INFO: Upload process completed {"uploaded_count":1,"errors_count":0} 
[2025-09-22 13:12:33] local.INFO: Deleted thumb thumbnail for media 6 {"media_id":6,"size":"thumb","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-150x150.jpg"} 
[2025-09-22 13:12:33] local.INFO: Deleted small thumbnail for media 6 {"media_id":6,"size":"small","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-300x300.jpg"} 
[2025-09-22 13:12:33] local.INFO: Deleted medium thumbnail for media 6 {"media_id":6,"size":"medium","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-600x600.jpg"} 
[2025-09-22 13:12:33] local.INFO: Deleted large thumbnail for media 6 {"media_id":6,"size":"large","path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b-1200x1200.jpg"} 
[2025-09-22 13:12:33] local.ERROR: Failed to delete media file {"media_id":6,"path":"media/my-folder/5a1b126e-7b3b-4042-b655-b931b5a25c4b.jpg","error":"Attempted to lazy load [metadata] on model [App\\Models\\Media] but lazy loading is disabled."} 
[2025-09-22 13:12:33] local.ERROR: Failed to delete folder {"folder_id":2,"folder_path":"my-folder","error":"Attempted to lazy load [metadata] on model [App\\Models\\Media] but lazy loading is disabled."} 
[2025-09-22 13:13:01] local.INFO: Deleted thumb thumbnail for media 4 {"media_id":4,"size":"thumb","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-150x150.png"} 
[2025-09-22 13:13:01] local.INFO: Deleted small thumbnail for media 4 {"media_id":4,"size":"small","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-300x300.png"} 
[2025-09-22 13:13:01] local.INFO: Deleted medium thumbnail for media 4 {"media_id":4,"size":"medium","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-600x600.png"} 
[2025-09-22 13:13:01] local.INFO: Deleted large thumbnail for media 4 {"media_id":4,"size":"large","path":"media/8d83a263-7005-4b36-b5cd-730adb83be58-1200x1200.png"} 
[2025-09-22 13:14:17] local.INFO: Deleted thumb thumbnail for media 7 {"media_id":7,"size":"thumb","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-150x150.png"} 
[2025-09-22 13:14:17] local.INFO: Deleted small thumbnail for media 7 {"media_id":7,"size":"small","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-300x300.png"} 
[2025-09-22 13:14:17] local.INFO: Deleted medium thumbnail for media 7 {"media_id":7,"size":"medium","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-600x600.png"} 
[2025-09-22 13:14:17] local.INFO: Deleted large thumbnail for media 7 {"media_id":7,"size":"large","path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5-1200x1200.png"} 
[2025-09-22 13:14:17] local.ERROR: Failed to delete media file {"media_id":7,"path":"media/my-folder/5f6409d7-086a-40d8-a52d-ccd362e185a5.png","error":"Attempted to lazy load [metadata] on model [App\\Models\\Media] but lazy loading is disabled."} 
[2025-09-22 13:14:17] local.ERROR: Failed to delete folder {"folder_id":2,"folder_path":"my-folder","error":"Attempted to lazy load [metadata] on model [App\\Models\\Media] but lazy loading is disabled."} 
[2025-09-22 13:17:39] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = App\\\\Mo...', false)
#2 D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = App\\\\Models\\\\M...', true)
#4 D:\\laragon\\www\\mls\\vendor\\psy\\psysh\\src\\Shell.php(1394): Psy\\Shell->setCode(' = App\\\\Models\\\\M...', true)
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = App\\\\Models\\\\M...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-09-22 13:18:24] local.ERROR: The "--table" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--table\" option does not exist. at D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('table', 'media_folders')
#1 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--table=media_f...')
#2 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--table=media_f...', true)
#3 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Command\\Command.php(276): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:18:35] local.ERROR: TTY mode is not supported on Windows platform. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\RuntimeException(code: 0): TTY mode is not supported on Windows platform. at D:\\laragon\\www\\mls\\vendor\\symfony\\process\\Process.php:1044)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\DbCommand.php(53): Symfony\\Component\\Process\\Process->setTty(true)
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\DbCommand->handle()
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#7 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\DbCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-09-22 13:23:50] local.INFO: Media upload request received {"files_count":4,"user_id":1,"request_data":{"folder_id":"3"}} 
[2025-09-22 13:23:50] local.INFO: Processing files for upload {"files_count":4,"user_id":1} 
[2025-09-22 13:23:50] local.INFO: Processing individual file {"filename":"info.gif","size":232,"mime_type":"image/gif"} 
[2025-09-22 13:23:50] local.INFO: Generated thumb thumbnail for media 10 {"media_id":10,"size":"thumb","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-150x150.gif","dimensions":"150x150"} 
[2025-09-22 13:23:50] local.INFO: Generated small thumbnail for media 10 {"media_id":10,"size":"small","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-300x300.gif","dimensions":"300x300"} 
[2025-09-22 13:23:51] local.INFO: Generated medium thumbnail for media 10 {"media_id":10,"size":"medium","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-600x600.gif","dimensions":"600x600"} 
[2025-09-22 13:23:51] local.INFO: Generated large thumbnail for media 10 {"media_id":10,"size":"large","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-1200x1200.gif","dimensions":"1200x1200"} 
[2025-09-22 13:23:51] local.INFO: Thumbnails generated for media {"media_id":10,"filename":"info.gif"} 
[2025-09-22 13:23:51] local.INFO: File uploaded successfully {"media_id":10,"filename":"info.gif"} 
[2025-09-22 13:23:51] local.INFO: Processing individual file {"filename":"intodns_logo.gif","size":3501,"mime_type":"image/gif"} 
[2025-09-22 13:23:51] local.INFO: Generated thumb thumbnail for media 11 {"media_id":11,"size":"thumb","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-150x150.gif","dimensions":"150x150"} 
[2025-09-22 13:23:51] local.INFO: Generated small thumbnail for media 11 {"media_id":11,"size":"small","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-300x300.gif","dimensions":"300x300"} 
[2025-09-22 13:23:52] local.INFO: Generated medium thumbnail for media 11 {"media_id":11,"size":"medium","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-600x600.gif","dimensions":"600x600"} 
[2025-09-22 13:23:52] local.INFO: Generated large thumbnail for media 11 {"media_id":11,"size":"large","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-1200x1200.gif","dimensions":"1200x1200"} 
[2025-09-22 13:23:52] local.INFO: Thumbnails generated for media {"media_id":11,"filename":"intodns_logo.gif"} 
[2025-09-22 13:23:52] local.INFO: File uploaded successfully {"media_id":11,"filename":"intodns_logo.gif"} 
[2025-09-22 13:23:52] local.INFO: Processing individual file {"filename":"intovps.png","size":21274,"mime_type":"image/png"} 
[2025-09-22 13:23:52] local.INFO: Generated thumb thumbnail for media 12 {"media_id":12,"size":"thumb","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-150x150.png","dimensions":"150x150"} 
[2025-09-22 13:23:52] local.INFO: Generated small thumbnail for media 12 {"media_id":12,"size":"small","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-300x300.png","dimensions":"300x300"} 
[2025-09-22 13:23:52] local.INFO: Generated medium thumbnail for media 12 {"media_id":12,"size":"medium","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-600x600.png","dimensions":"600x600"} 
[2025-09-22 13:23:52] local.INFO: Generated large thumbnail for media 12 {"media_id":12,"size":"large","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-1200x1200.png","dimensions":"1200x1200"} 
[2025-09-22 13:23:52] local.INFO: Thumbnails generated for media {"media_id":12,"filename":"intovps.png"} 
[2025-09-22 13:23:52] local.INFO: File uploaded successfully {"media_id":12,"filename":"intovps.png"} 
[2025-09-22 13:23:52] local.INFO: Processing individual file {"filename":"pass.gif","size":272,"mime_type":"image/gif"} 
[2025-09-22 13:23:52] local.INFO: Generated thumb thumbnail for media 13 {"media_id":13,"size":"thumb","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-150x150.gif","dimensions":"150x150"} 
[2025-09-22 13:23:52] local.INFO: Generated small thumbnail for media 13 {"media_id":13,"size":"small","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-300x300.gif","dimensions":"300x300"} 
[2025-09-22 13:23:53] local.INFO: Generated medium thumbnail for media 13 {"media_id":13,"size":"medium","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-600x600.gif","dimensions":"600x600"} 
[2025-09-22 13:23:53] local.INFO: Generated large thumbnail for media 13 {"media_id":13,"size":"large","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-1200x1200.gif","dimensions":"1200x1200"} 
[2025-09-22 13:23:53] local.INFO: Thumbnails generated for media {"media_id":13,"filename":"pass.gif"} 
[2025-09-22 13:23:53] local.INFO: File uploaded successfully {"media_id":13,"filename":"pass.gif"} 
[2025-09-22 13:23:53] local.INFO: Upload process completed {"uploaded_count":4,"errors_count":0} 
[2025-09-22 13:34:58] local.INFO: Deleted thumb thumbnail for media 10 {"media_id":10,"size":"thumb","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-150x150.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted small thumbnail for media 10 {"media_id":10,"size":"small","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-300x300.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted medium thumbnail for media 10 {"media_id":10,"size":"medium","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-600x600.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted large thumbnail for media 10 {"media_id":10,"size":"large","path":"media/hello-folder/0d78d102-1d4b-4057-b6b9-fbea6a1891c3-1200x1200.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted thumb thumbnail for media 11 {"media_id":11,"size":"thumb","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-150x150.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted small thumbnail for media 11 {"media_id":11,"size":"small","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-300x300.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted medium thumbnail for media 11 {"media_id":11,"size":"medium","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-600x600.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted large thumbnail for media 11 {"media_id":11,"size":"large","path":"media/hello-folder/a5e5ae7c-d202-4b1c-90a3-a4c5e0585ab4-1200x1200.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted thumb thumbnail for media 12 {"media_id":12,"size":"thumb","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-150x150.png"} 
[2025-09-22 13:34:58] local.INFO: Deleted small thumbnail for media 12 {"media_id":12,"size":"small","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-300x300.png"} 
[2025-09-22 13:34:58] local.INFO: Deleted medium thumbnail for media 12 {"media_id":12,"size":"medium","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-600x600.png"} 
[2025-09-22 13:34:58] local.INFO: Deleted large thumbnail for media 12 {"media_id":12,"size":"large","path":"media/hello-folder/6426fa10-0954-4ccb-8196-e66da176c6ec-1200x1200.png"} 
[2025-09-22 13:34:58] local.INFO: Deleted thumb thumbnail for media 13 {"media_id":13,"size":"thumb","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-150x150.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted small thumbnail for media 13 {"media_id":13,"size":"small","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-300x300.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted medium thumbnail for media 13 {"media_id":13,"size":"medium","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-600x600.gif"} 
[2025-09-22 13:34:58] local.INFO: Deleted large thumbnail for media 13 {"media_id":13,"size":"large","path":"media/hello-folder/de026440-45a3-4370-b91c-1e7125ac2c87-1200x1200.gif"} 
[2025-09-22 13:48:42] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../livewire/livewire/src/Mechanisms/CompileLivewireTags/LivewireTagPrecompiler.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../livewire/livewire/src/Mechanisms/CompileLivewireTags/LivewireTagPrecompiler.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\mls\\vendor\\livewire\\livewire\\src\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags.php(11): Composer\\Autoload\\ClassLoader->loadClass('Livewire\\\\Mechan...')
#5 D:\\laragon\\www\\mls\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php(77): Livewire\\Mechanisms\\CompileLivewireTags\\CompileLivewireTags->boot()
#6 D:\\laragon\\www\\mls\\vendor\\livewire\\livewire\\src\\LivewireServiceProvider.php(19): Livewire\\LivewireServiceProvider->bootMechanisms()
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Livewire\\LivewireServiceProvider->boot()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Livewire\\LivewireServiceProvider))
#14 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Livewire\\LivewireServiceProvider), 'Livewire\\\\Livewi...')
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-09-22 13:55:05] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:07] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:08] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:08] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:09] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:10] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:11] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:11] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:12] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:12] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:13] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:14] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:14] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:15] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:16] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:16] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:17] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:18] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:19] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:21] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:21] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:22] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:23] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:23] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:24] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:25] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:26] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:27] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:28] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:28] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:29] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:30] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:31] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:31] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:32] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:33] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:33] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:34] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:35] local.ERROR: Class "Filament\PanelProvider" not found {"exception":"[object] (Error(code: 0): Class \"Filament\\PanelProvider\" not found at D:\\laragon\\www\\mls\\app\\Providers\\Filament\\AdminPanelProvider.php:23)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#10 {main}
"} 
[2025-09-22 13:55:37] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:38] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:40] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:41] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:42] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:43] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:44] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:45] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:46] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:47] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:47] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:48] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:49] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:50] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:50] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:51] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:52] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:52] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:53] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:54] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:54] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:55] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:56] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:56] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:57] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:58] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:58] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:55:59] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:00] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:01] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:01] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:02] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:03] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:05] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:07] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:08] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:10] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:11] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:12] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:13] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:14] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:14] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:15] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:16] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:17] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:18] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:19] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:20] local.ERROR: include(D:\laragon\www\mls\vendor\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\laragon\\www\\mls\\vendor\\composer/../../app/Providers/Filament/AdminPanelProvider.php): Failed to open stream: No such file or directory at D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php:571)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(258): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\lara...', 'D:\\\\laragon\\\\www\\\\...', 571)
#2 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include('D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Providers\\\\F...')
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(52): class_exists('App\\\\Providers\\\\F...')
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(34): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->mergeAdditionalProviders(Object(Illuminate\\Foundation\\Application))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(474): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(196): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-09-22 13:56:25] local.ERROR: There are no commands defined in the "filament" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"filament\" namespace. at D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php:677)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(726): Symfony\\Component\\Console\\Application->findNamespace('filament')
#1 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(301): Symfony\\Component\\Console\\Application->find('filament:upgrad...')
#2 D:\\laragon\\www\\mls\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 D:\\laragon\\www\\mls\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-09-22 13:59:05] local.ERROR: Interface "Filament\Models\Contracts\FilamentUser" not found {"exception":"[object] (Error(code: 0): Interface \"Filament\\Models\\Contracts\\FilamentUser\" not found at D:\\laragon\\www\\mls\\app\\Models\\User.php:110)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(57): Illuminate\\Auth\\SessionGuard->user()
#6 [internal function]: Illuminate\\Auth\\AuthManager->Illuminate\\Auth\\{closure}(NULL)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php(89): call_user_func(Object(Closure), NULL)
#8 [internal function]: Illuminate\\Auth\\AuthServiceProvider->Illuminate\\Auth\\{closure}(NULL)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php(624): call_user_func(Object(Closure), NULL)
#10 D:\\laragon\\www\\mls\\app\\Http\\Middleware\\HandleInertiaRequests.php(45): Illuminate\\Http\\Request->user()
#11 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(89): App\\Http\\Middleware\\HandleInertiaRequests->share(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#57 {main}
"} 
[2025-09-22 13:59:11] local.ERROR: Interface "Filament\Models\Contracts\FilamentUser" not found {"exception":"[object] (Error(code: 0): Interface \"Filament\\Models\\Contracts\\FilamentUser\" not found at D:\\laragon\\www\\mls\\app\\Models\\User.php:110)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(251): Illuminate\\Auth\\SessionGuard->user()
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(221): Illuminate\\Auth\\SessionGuard->id()
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(208): Illuminate\\Session\\DatabaseSessionHandler->userId()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(194): Illuminate\\Session\\DatabaseSessionHandler->addUserInformation(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\DatabaseSessionHandler->Illuminate\\Session\\{closure}(Array)
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(193): tap(Array, Object(Closure))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(133): Illuminate\\Session\\DatabaseSessionHandler->getDefaultPayload('a:5:{s:6:\"_toke...')
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(176): Illuminate\\Session\\DatabaseSessionHandler->write('lMyh2jKgwfRLkA3...', 'a:5:{s:6:\"_toke...')
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(245): Illuminate\\Session\\Store->save()
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(130): Illuminate\\Session\\Middleware\\StartSession->saveSession(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#50 {main}
"} 
[2025-09-22 14:01:32] local.ERROR: Interface "Filament\Models\Contracts\FilamentUser" not found {"exception":"[object] (Error(code: 0): Interface \"Filament\\Models\\Contracts\\FilamentUser\" not found at D:\\laragon\\www\\mls\\app\\Models\\User.php:110)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(57): Illuminate\\Auth\\SessionGuard->user()
#6 [internal function]: Illuminate\\Auth\\AuthManager->Illuminate\\Auth\\{closure}(NULL)
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php(89): call_user_func(Object(Closure), NULL)
#8 [internal function]: Illuminate\\Auth\\AuthServiceProvider->Illuminate\\Auth\\{closure}(NULL)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php(624): call_user_func(Object(Closure), NULL)
#10 D:\\laragon\\www\\mls\\app\\Http\\Middleware\\HandleInertiaRequests.php(45): Illuminate\\Http\\Request->user()
#11 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(89): App\\Http\\Middleware\\HandleInertiaRequests->share(Object(Illuminate\\Http\\Request))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#57 {main}
"} 
[2025-09-22 14:01:34] local.ERROR: Interface "Filament\Models\Contracts\FilamentUser" not found {"exception":"[object] (Error(code: 0): Interface \"Filament\\Models\\Contracts\\FilamentUser\" not found at D:\\laragon\\www\\mls\\app\\Models\\User.php:110)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(571): include()
#1 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(251): Illuminate\\Auth\\SessionGuard->user()
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(221): Illuminate\\Auth\\SessionGuard->id()
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(208): Illuminate\\Session\\DatabaseSessionHandler->userId()
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(194): Illuminate\\Session\\DatabaseSessionHandler->addUserInformation(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\DatabaseSessionHandler->Illuminate\\Session\\{closure}(Array)
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(193): tap(Array, Object(Closure))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(133): Illuminate\\Session\\DatabaseSessionHandler->getDefaultPayload('a:5:{s:6:\"_toke...')
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(176): Illuminate\\Session\\DatabaseSessionHandler->write('lMyh2jKgwfRLkA3...', 'a:5:{s:6:\"_toke...')
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(245): Illuminate\\Session\\Store->save()
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(130): Illuminate\\Session\\Middleware\\StartSession->saveSession(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#50 {main}
"} 
[2025-09-22 14:02:32] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:02:57] local.ERROR: syntax error, unexpected identifier "D", expecting "function" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected identifier \"D\", expecting \"function\" at D:\\laragon\\www\\mls\\app\\Models\\User.php:114)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(57): Illuminate\\Auth\\SessionGuard->user()
#5 [internal function]: Illuminate\\Auth\\AuthManager->Illuminate\\Auth\\{closure}(NULL)
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthServiceProvider.php(89): call_user_func(Object(Closure), NULL)
#7 [internal function]: Illuminate\\Auth\\AuthServiceProvider->Illuminate\\Auth\\{closure}(NULL)
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Request.php(624): call_user_func(Object(Closure), NULL)
#9 D:\\laragon\\www\\mls\\app\\Http\\Middleware\\HandleInertiaRequests.php(45): Illuminate\\Http\\Request->user()
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(89): App\\Http\\Middleware\\HandleInertiaRequests->share(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#56 {main}
"} 
[2025-09-22 14:02:58] local.ERROR: syntax error, unexpected identifier "D", expecting "function" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected identifier \"D\", expecting \"function\" at D:\\laragon\\www\\mls\\app\\Models\\User.php:114)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\laragon\\\\www\\\\...')
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(209): Composer\\Autoload\\ClassLoader->loadClass('App\\\\Models\\\\User')
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(55): Illuminate\\Auth\\EloquentUserProvider->createModel()
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(180): Illuminate\\Auth\\EloquentUserProvider->retrieveById(1)
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(251): Illuminate\\Auth\\SessionGuard->user()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(221): Illuminate\\Auth\\SessionGuard->id()
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(208): Illuminate\\Session\\DatabaseSessionHandler->userId()
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(194): Illuminate\\Session\\DatabaseSessionHandler->addUserInformation(Array)
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\DatabaseSessionHandler->Illuminate\\Session\\{closure}(Array)
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(193): tap(Array, Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(133): Illuminate\\Session\\DatabaseSessionHandler->getDefaultPayload('a:5:{s:6:\"_toke...')
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(176): Illuminate\\Session\\DatabaseSessionHandler->write('lMyh2jKgwfRLkA3...', 'a:5:{s:6:\"_toke...')
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(245): Illuminate\\Session\\Store->save()
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(130): Illuminate\\Session\\Middleware\\StartSession->saveSession(Object(Illuminate\\Http\\Request))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#49 {main}
"} 
[2025-09-22 14:03:35] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:04:30] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:04:44] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:04:58] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:05:31] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:05:38] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:05:56] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
[2025-09-22 14:06:59] local.ERROR: Class "Filament\Facades\Filament" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"Filament\\Facades\\Filament\" not found at D:\\laragon\\www\\mls\\app\\Http\\Controllers\\DashboardController.php:22)
[stacktrace]
#0 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\DashboardController->__invoke()
#1 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('__invoke', Array)
#2 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), '__invoke')
#3 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\laragon\\www\\mls\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(96): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\laragon\\www\\mls\\vendor\\laravel\\jetstream\\src\\Http\\Middleware\\ShareInertiaData.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Laravel\\Jetstream\\Http\\Middleware\\ShareInertiaData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#20 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\mls\\public\\index.php(19): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\mls\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\laragon\\\\www\\\\...')
#60 {main}
"} 
