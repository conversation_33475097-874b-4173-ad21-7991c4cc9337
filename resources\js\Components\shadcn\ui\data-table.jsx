import { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le, useRef } from 'react';
import { But<PERSON> } from '@/Components/shadcn/ui/button';
import { Input } from '@/Components/shadcn/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/Components/shadcn/ui/table';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/Components/shadcn/ui/pagination';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/shadcn/ui/select';
import { SearchBar } from '@/Components/shadcn/ui/search-bar';
import { Checkbox } from '@/Components/shadcn/ui/checkbox';
import { X, ArrowUpDown, ArrowUp, ArrowDown, Filter, Plus, Minus, Settings2, <PERSON>otateCcw, Loader2 } from 'lucide-react';
import { Toolt<PERSON>, TooltipTrigger, TooltipContent, TooltipProvider } from '@/Components/shadcn/ui/tooltip';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/Components/shadcn/ui/popover";
import { Label } from "@/Components/shadcn/ui/label";
import { ScrollArea } from "@/Components/shadcn/ui/scroll-area";
import { DatePicker } from "@/Components/shadcn/ui/date-picker";
import { Switch } from "@/Components/shadcn/ui/switch";
import axios from 'axios';
import { toast } from 'sonner';

const DataTable = forwardRef(({
    columns: initialColumns,
    searchPlaceholder = "Search...",
    searchRoute,
    actions = [],
    onRowClick,
    selectable = false,
    onSelectionChange,
    selectedItems: externalSelectedItems,
    itemKey = 'id',
    pageName,
}, ref) => {
    const [search, setSearch] = useState('');
    const [itemsPerPage, setItemsPerPage] = useState('10');
    const [internalSelectedItems, setInternalSelectedItems] = useState([]);
    const [sortColumn, setSortColumn] = useState(null);
    const [sortDirection, setSortDirection] = useState('asc');
    const [filters, setFilters] = useState({});
    const [dateRanges, setDateRanges] = useState({});
    const [columnVisibility, setColumnVisibility] = useState({});
    const [data, setData] = useState({ data: [], total: 0, current_page: 1, last_page: 1, from: 0, to: 0 });
    const [loading, setLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [isInitialLoad, setIsInitialLoad] = useState(true);

    const fetchTimeoutRef = useRef(null);

    // Initialize column visibility
    useEffect(() => {
        const initialVisibility = {};
        initialColumns.forEach(column => {
            initialVisibility[column.accessorKey] = column.visible !== false;
        });
        setColumnVisibility(initialVisibility);
    }, [initialColumns]);

    // Use external selected items if provided
    const selectedItems = externalSelectedItems || internalSelectedItems;
    const setSelectedItems = onSelectionChange || setInternalSelectedItems;

    useImperativeHandle(ref, () => ({
        fetchData: () => {
            if (fetchTimeoutRef.current) {
                clearTimeout(fetchTimeoutRef.current);
            }
            const params = {
                page: currentPage,
                search: search || '',
                per_page: itemsPerPage,
                sort: sortColumn,
                direction: sortDirection,
                ...filters,
                ...Object.fromEntries(
                    Object.entries(dateRanges).flatMap(([key, range]) => {
                        const filters = [];
                        if (range?.from) {
                            filters.push([`filter_${key}_from`, range.from.toISOString().split('T')[0]]);
                            if (range.to) {
                                filters.push([`filter_${key}_to`, range.to.toISOString().split('T')[0]]);
                            }
                        }
                        return filters;
                    })
                )
            };
            fetchData(params);
        }
    }));

    const getItemKey = (item) => {
        if (typeof itemKey === 'function') {
            return itemKey(item);
        }
        return item[itemKey];
    };

    const isItemSelected = (item) => {
        const key = getItemKey(item);
        return selectedItems.some(selectedItem => getItemKey(selectedItem) === key);
    };

    const handleItemSelection = (item, checked) => {
        const key = getItemKey(item);
        if (checked) {
            setSelectedItems([...selectedItems, item]);
        } else {
            setSelectedItems(selectedItems.filter(selectedItem => getItemKey(selectedItem) !== key));
        }
    };

    const handleSelectAll = (checked) => {
        if (checked) {
            const newSelectedItems = [...selectedItems];
            data.data.forEach(item => {
                if (!isItemSelected(item)) {
                    newSelectedItems.push(item);
                }
            });
            setSelectedItems(newSelectedItems);
        } else {
            const currentPageKeys = data.data.map(item => getItemKey(item));
            setSelectedItems(selectedItems.filter(item => !currentPageKeys.includes(getItemKey(item))));
        }
    };

    const fetchData = async (params) => {
        if (!searchRoute) {
            console.warn('DataTable: searchRoute is not provided');
            return;
        }

        setLoading(true);
        try {
            console.log('DataTable: Fetching data with params:', params);
            const response = await axios.get(searchRoute, { params });
            setData(response.data);
        } catch (error) {
            console.error('DataTable: Error fetching data:', error);
            toast.error('Failed to fetch data');
        } finally {
            setLoading(false);
        }
    };

    // Debounced fetch effect
    useEffect(() => {
        if (fetchTimeoutRef.current) {
            clearTimeout(fetchTimeoutRef.current);
        }

        if (isInitialLoad) {
            setIsInitialLoad(false);
            fetchTimeoutRef.current = setTimeout(() => {
                const params = {
                    page: currentPage,
                    search: search || '',
                    per_page: itemsPerPage,
                    sort: sortColumn,
                    direction: sortDirection,
                    ...filters,
                    ...Object.fromEntries(
                        Object.entries(dateRanges || {}).flatMap(([key, range]) => {
                            const filters = [];
                            if (range?.from) {
                                try {
                                    filters.push([`filter_${key}_from`, range.from.toISOString().split('T')[0]]);
                                    if (range.to) {
                                        filters.push([`filter_${key}_to`, range.to.toISOString().split('T')[0]]);
                                    }
                                } catch (error) {
                                    console.warn('DataTable: Error formatting date range:', error);
                                }
                            }
                            return filters;
                        })
                    )
                };
                fetchData(params);
            }, 100);
        } else {
            // Reset to page 1 when search/filters change
            if (currentPage !== 1) {
                setCurrentPage(1);
                return;
            }

            fetchTimeoutRef.current = setTimeout(() => {
                const params = {
                    page: currentPage,
                    search: search || '',
                    per_page: itemsPerPage,
                    sort: sortColumn,
                    direction: sortDirection,
                    ...filters,
                    ...Object.fromEntries(
                        Object.entries(dateRanges || {}).flatMap(([key, range]) => {
                            const filters = [];
                            if (range?.from) {
                                try {
                                    filters.push([`filter_${key}_from`, range.from.toISOString().split('T')[0]]);
                                    if (range.to) {
                                        filters.push([`filter_${key}_to`, range.to.toISOString().split('T')[0]]);
                                    }
                                } catch (error) {
                                    console.warn('DataTable: Error formatting date range:', error);
                                }
                            }
                            return filters;
                        })
                    )
                };
                fetchData(params);
            }, 100); // 100ms debounce
        }
    }, [isInitialLoad, currentPage, search, itemsPerPage, sortColumn, sortDirection, filters, dateRanges]);

    const handleSearchChange = (e) => {
        setSearch(e.target.value);
    };

    const handleSort = (column) => {
        if (!column.sortable) return;

        if (sortColumn === column.accessorKey) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(column.accessorKey);
            setSortDirection('asc');
        }
    };

    const handleFilterChange = (columnKey, value) => {
        setFilters(prev => ({
            ...prev,
            [`filter_${columnKey}`]: value
        }));
    };

    const handleDateRangeChange = (columnKey, range) => {
        setDateRanges(prev => ({
            ...prev,
            [columnKey]: range
        }));
    };

    const clearAllFilters = () => {
        setFilters({});
        setDateRanges({});
        setSearch('');
    };

    const toggleColumnVisibility = (columnKey) => {
        setColumnVisibility(prev => ({
            ...prev,
            [columnKey]: !prev[columnKey]
        }));
    };

    // Get visible columns
    const visibleColumns = initialColumns.filter(column => columnVisibility[column.accessorKey]);

    // Get filterable columns
    const filterableColumns = initialColumns.filter(column => column.filter);

    // Get active filters count
    const activeFilters = Object.entries(filters).filter(([key, value]) => value && value.trim() !== '').length +
                         Object.entries(dateRanges).filter(([key, range]) => range?.from).length;

    const getSortIcon = (column) => {
        if (!column.sortable) return null;

        if (sortColumn === column.accessorKey) {
            return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
        }
        return <ArrowUpDown className="h-4 w-4" />;
    };

    const isAllCurrentPageSelected = data.data.length > 0 && data.data.every(item => isItemSelected(item));
    const isSomeCurrentPageSelected = data.data.some(item => isItemSelected(item));

    return (
        <div className="space-y-5">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="w-full sm:max-w-md flex flex-col sm:flex-row items-start sm:items-center gap-2">
                    <SearchBar
                        value={search}
                        onChange={handleSearchChange}
                        placeholder={searchPlaceholder}
                        className="w-full"
                    />
                    <div className="flex items-center gap-2 w-full sm:w-auto">
                        {filterableColumns.length > 0 && (
                            <div className="flex items-center gap-2 whitespace-nowrap">
                                <Popover>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            className={`${activeFilters > 0 ? 'text-primary' : ''}`}
                                        >
                                            <Filter className="h-4 w-4" />
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-[calc(100vw-2rem)] sm:w-[500px] p-0" align="start" side="bottom">
                                        <div className="p-4 space-y-4">
                                            <div className="flex items-center justify-between">
                                                <h4 className="font-medium leading-none">Filters</h4>
                                                {activeFilters > 0 && (
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={clearAllFilters}
                                                        className="h-auto px-2 py-1 text-xs"
                                                    >
                                                        <RotateCcw className="h-3 w-3 mr-1" />
                                                        Clear all
                                                    </Button>
                                                )}
                                            </div>
                                            <ScrollArea className="h-[350px] pr-4">
                                                <div className="space-y-6">
                                                    {filterableColumns.map((column) => (
                                                        <div key={column.accessorKey} className="space-y-3">
                                                            <Label className="text-sm font-medium text-foreground">
                                                                {column.header}
                                                            </Label>
                                                            {column.filter?.type === 'text' && (
                                                                <Input
                                                                    value={filters[`filter_${column.accessorKey}`] || ''}
                                                                    onChange={(e) => handleFilterChange(column.accessorKey, e.target.value)}
                                                                    placeholder={column.filter.placeholder || 'Filter...'}
                                                                    className="w-full"
                                                                />
                                                            )}
                                                            {column.filter?.type === 'select' && (
                                                                <Select
                                                                    value={filters[`filter_${column.accessorKey}`] || 'all'}
                                                                    onValueChange={(value) => handleFilterChange(column.accessorKey, value === 'all' ? '' : value)}
                                                                >
                                                                    <SelectTrigger className="w-full">
                                                                        <SelectValue placeholder={column.filter.placeholder || 'Select option...'} />
                                                                    </SelectTrigger>
                                                                    <SelectContent>
                                                                        <SelectItem value="all">All</SelectItem>
                                                                        {column.filter.options?.map((option) => (
                                                                            <SelectItem key={option.value} value={option.value}>
                                                                                {option.label}
                                                                            </SelectItem>
                                                                        ))}
                                                                    </SelectContent>
                                                                </Select>
                                                            )}
                                                            {column.filter?.type === 'date' && (
                                                                <div className="w-full space-y-2">
                                                                    <DatePicker
                                                                        date={dateRanges[column.accessorKey]}
                                                                        onSelect={(range) => handleDateRangeChange(column.accessorKey, range)}
                                                                        mode="range"
                                                                        placeholder={column.filter.placeholder || 'Select date range...'}
                                                                        numberOfMonths={2}
                                                                        className="w-full"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                </div>
                                            </ScrollArea>
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            </div>
                        )}
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    {/* Column Visibility */}
                    <Popover>
                        <PopoverTrigger asChild>
                            <Button variant="outline" size="icon">
                                <Settings2 className="h-4 w-4" />
                            </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[200px]">
                            <div className="space-y-4">
                                <h4 className="font-medium leading-none">Toggle columns</h4>
                                <div className="space-y-2">
                                    {initialColumns.map((column) => (
                                        <div key={column.accessorKey} className="flex items-center space-x-2">
                                            <Switch
                                                id={column.accessorKey}
                                                checked={columnVisibility[column.accessorKey]}
                                                onCheckedChange={() => toggleColumnVisibility(column.accessorKey)}
                                            />
                                            <Label htmlFor={column.accessorKey} className="text-sm">
                                                {column.header}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </PopoverContent>
                    </Popover>

                    {/* Items per page */}
                    <Select value={itemsPerPage} onValueChange={setItemsPerPage}>
                        <SelectTrigger className="w-[100px]">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="25">25</SelectItem>
                            <SelectItem value="50">50</SelectItem>
                            <SelectItem value="100">100</SelectItem>
                        </SelectContent>
                    </Select>

                    {/* Action buttons */}
                    {actions.map((action, index) => (
                        <TooltipProvider key={index}>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        variant={action.variant || "default"}
                                        size={action.size || "default"}
                                        onClick={() => action.onClick?.(selectedItems)}
                                        disabled={action.disabled || (action.requiresSelection && selectedItems.length === 0)}
                                        className={action.className}
                                    >
                                        {action.icon && <action.icon className="h-4 w-4 mr-2" />}
                                        {action.label}
                                    </Button>
                                </TooltipTrigger>
                                {action.tooltip && (
                                    <TooltipContent>
                                        <p>{action.tooltip}</p>
                                    </TooltipContent>
                                )}
                            </Tooltip>
                        </TooltipProvider>
                    ))}
                </div>
            </div>

            {/* Table */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            {selectable && (
                                <TableHead className="w-px">
                                    <Checkbox
                                        checked={isAllCurrentPageSelected}
                                        onCheckedChange={handleSelectAll}
                                        indeterminate={isSomeCurrentPageSelected && !isAllCurrentPageSelected}
                                        aria-label="Select all"
                                    />
                                </TableHead>
                            )}
                            {visibleColumns.map((column) => (
                                <TableHead
                                    key={column.accessorKey}
                                    className={column.sortable ? "cursor-pointer select-none" : ""}
                                    onClick={() => handleSort(column)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <span>{column.header}</span>
                                        {getSortIcon(column)}
                                    </div>
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            <TableRow>
                                <TableCell colSpan={visibleColumns.length + (selectable ? 1 : 0)} className="text-center h-24">
                                    <Loader2 className="h-5 w-5 mx-auto animate-spin text-muted-foreground" />
                                </TableCell>
                            </TableRow>
                        ) : (!data?.data || data.data.length === 0 ? (
                            <TableRow>
                                <TableCell colSpan={visibleColumns.length + (selectable ? 1 : 0)} className="text-center h-24 text-muted-foreground">
                                    No items found
                                </TableCell>
                            </TableRow>
                        ) : (
                            data.data.map((item, index) => (
                                <TableRow
                                    key={index}
                                    className={onRowClick ? "cursor-pointer hover:bg-muted/50" : ""}
                                    onClick={() => onRowClick?.(item)}
                                >
                                    {selectable && (
                                        <TableCell className="w-px">
                                            <Checkbox
                                                checked={isItemSelected(item)}
                                                onCheckedChange={(checked) => handleItemSelection(item, checked)}
                                                onClick={(e) => e.stopPropagation()}
                                                aria-label={`Select row ${index + 1}`}
                                            />
                                        </TableCell>
                                    )}
                                    {visibleColumns.map((column) => (
                                        <TableCell key={column.accessorKey}>
                                            {column.cell ? column.cell(item) : item[column.accessorKey]}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {data.last_page > 1 && (
                <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                        Showing {data.from} to {data.to} of {data.total} results
                        {selectedItems.length > 0 && (
                            <span className="ml-2">
                                ({selectedItems.length} selected)
                            </span>
                        )}
                    </div>
                    <Pagination>
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1}
                                />
                            </PaginationItem>

                            {/* Page numbers */}
                            {Array.from({ length: Math.min(5, data.last_page) }, (_, i) => {
                                let pageNum;
                                if (data.last_page <= 5) {
                                    pageNum = i + 1;
                                } else if (currentPage <= 3) {
                                    pageNum = i + 1;
                                } else if (currentPage >= data.last_page - 2) {
                                    pageNum = data.last_page - 4 + i;
                                } else {
                                    pageNum = currentPage - 2 + i;
                                }

                                return (
                                    <PaginationItem key={pageNum}>
                                        <PaginationLink
                                            onClick={() => setCurrentPage(pageNum)}
                                            isActive={currentPage === pageNum}
                                        >
                                            {pageNum}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            })}

                            {data.last_page > 5 && currentPage < data.last_page - 2 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            <PaginationItem>
                                <PaginationNext
                                    onClick={() => setCurrentPage(Math.min(data.last_page, currentPage + 1))}
                                    disabled={currentPage === data.last_page}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </div>
            )}
        </div>
    );
});

DataTable.displayName = "DataTable";

export { DataTable };
