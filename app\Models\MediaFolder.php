<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class MediaFolder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'path',
        'parent_id',
        'created_by',
        'company_id',
        'description',
    ];

    protected $casts = [
    ];

    protected $appends = [
        'full_path',
        'media_count',
        'subfolder_count',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($folder) {
            if (empty($folder->slug)) {
                $folder->slug = Str::slug($folder->name);
            }

            // Generate full path
            $folder->path = $folder->generatePath();
        });

        static::updating(function ($folder) {
            if ($folder->isDirty('name') || $folder->isDirty('parent_id')) {
                $folder->path = $folder->generatePath();
            }
        });

        static::deleting(function ($folder) {
            // Move all media files to parent folder or root
            $folder->mediaFiles()->update([
                'folder_id' => $folder->parent_id
            ]);

            // Move all subfolders to parent folder or root
            $folder->subfolders()->update([
                'parent_id' => $folder->parent_id
            ]);
        });
    }

    /**
     * Get the parent folder.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(MediaFolder::class, 'parent_id');
    }

    /**
     * Get the subfolders.
     */
    public function subfolders(): HasMany
    {
        return $this->hasMany(MediaFolder::class, 'parent_id');
    }

    /**
     * Get all descendants (recursive subfolders).
     */
    public function descendants(): HasMany
    {
        return $this->subfolders()->with('descendants');
    }

    /**
     * Get the creator of the folder.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the company that owns the folder.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'company_id');
    }

    /**
     * Get the media files in this folder.
     */
    public function mediaFiles(): HasMany
    {
        return $this->hasMany(Media::class, 'folder_id');
    }

    /**
     * Generate the full path for this folder.
     */
    public function generatePath(): string
    {
        if ($this->parent) {
            return $this->parent->path . '/' . $this->slug;
        }

        return $this->slug;
    }

    /**
     * Get the full path attribute.
     */
    public function getFullPathAttribute(): string
    {
        return $this->path;
    }

    /**
     * Get the media count attribute.
     */
    public function getMediaCountAttribute(): int
    {
        return $this->mediaFiles()->count();
    }

    /**
     * Get the subfolder count attribute.
     */
    public function getSubfolderCountAttribute(): int
    {
        return $this->subfolders()->count();
    }

    /**
     * Get all ancestors (parent folders up to root).
     */
    public function getAncestors(): array
    {
        $ancestors = [];
        $current = $this->parent;

        while ($current) {
            array_unshift($ancestors, $current);
            $current = $current->parent;
        }

        return $ancestors;
    }

    /**
     * Check if user can access this folder.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Super admin can access all folders
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        // Company admin can access company folders
        if ($user->user_type->canManageCompanyMedia()) {
            return $this->company_id === $user->currentTeam?->id;
        }

        // Individual agents can access their own folders
        if ($user->can_upload_media) {
            return $this->created_by === $user->id;
        }

        return false;
    }

    /**
     * Create the physical directory if it doesn't exist.
     */
    public function createPhysicalDirectory(): bool
    {
        $fullPath = storage_path('app/public/media/' . $this->path);

        if (!file_exists($fullPath)) {
            return mkdir($fullPath, 0755, true);
        }

        return true;
    }

    /**
     * Scope to get root folders (no parent).
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get folders accessible by user.
     */
    public function scopeAccessibleBy($query, User $user)
    {
        if ($user->user_type->canAccessAllMedia()) {
            return $query;
        }

        if ($user->user_type->canManageCompanyMedia()) {
            return $query->where('company_id', $user->currentTeam?->id);
        }

        if ($user->can_upload_media) {
            return $query->where('created_by', $user->id);
        }

        return $query->whereRaw('1 = 0'); // No access
    }
}
