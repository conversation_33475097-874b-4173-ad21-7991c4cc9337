{"type": "module", "private": true, "scripts": {"build": "vite build", "dev": "vite", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@iconify/react": "^5.2.0", "@inertiajs/react": "^2.0.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.0.12", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "@unhead/addons": "^2.0.0-rc.9", "@unhead/react": "^2.0.0-rc.9", "@unovis/ts": "^1.5.1", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.2", "change-case": "^5.4.4", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.1.2", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lucide-react": "^0.475.0", "next-themes": "^0.4.5", "postcss": "^8.5.3", "react": "^19.0.0", "react-day-picker": "9.5.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.12", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "vite": "6.1.6", "ziggy-js": "^2.5.2", "zod": "^3.24.2"}, "devDependencies": {"@antfu/eslint-config": "^4.8.1", "@eslint-react/eslint-plugin": "^1.31.0", "eslint": "^9.22.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "laravel-vite-plugin": "^1.2.0"}}