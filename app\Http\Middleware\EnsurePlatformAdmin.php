<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\UserType;

class EnsurePlatformAdmin
{
    /**
     * Handle an incoming request to ensure only Platform Administrators can access.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            abort(401, 'Unauthenticated');
        }

        if ($user->user_type !== UserType::PLATFORM_ADMINISTRATOR) {
            abort(403, 'Platform Administrator access required');
        }

        return $next($request);
    }
}
