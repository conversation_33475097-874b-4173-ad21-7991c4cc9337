"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowUpDown, <PERSON>rash2, <PERSON>, <PERSON> } from "lucide-react"
import { Checkbox } from "@/Components/shadcn/ui/checkbox"
import { Button } from "@/Components/shadcn/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/Components/shadcn/ui/dropdown-menu"
import { DataTableColumnHeader } from "@/Components/shadcn/ui/data-table-column-header"
import { Badge } from "@/Components/shadcn/ui/badge"
import { Link, router } from '@inertiajs/react'
import { route } from 'ziggy-js'
import { toast } from 'sonner'

export const columns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => <div className="font-medium">{row.getValue("name")}</div>,
  },
  {
    accessorKey: "description",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Description" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[500px] truncate text-muted-foreground">
        {row.getValue("description")}
      </div>
    ),
  },
  {
    accessorKey: "price",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Price" />
    ),
    cell: ({ row }) => {
      const price = parseFloat(row.getValue("price"))
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(price)

      return <div className="font-medium">{formatted}</div>
    },
  },
  {
    accessorKey: "billing_period",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Billing Period" />
    ),
    cell: ({ row }) => {
      const period = row.getValue("billing_period")
      return (
        <Badge variant="outline" className="capitalize">
          {period}
        </Badge>
      )
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status")
      return (
        <Badge variant={status === "active" ? "default" : "secondary"}>
          {status}
        </Badge>
      )
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const plan = row.original

      const handleDelete = () => {
        if (confirm(`Are you sure you want to delete the plan "${plan.name}"?`)) {
          router.delete(route('admin.plans.destroy', plan.id), {
            onSuccess: () => {
              toast.success('Plan deleted successfully')
              window.location.reload() // Refresh the page to update data
            },
            onError: () => {
              toast.error('Failed to delete plan')
            }
          })
        }
      }

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(plan.id)}
            >
              Copy plan ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={route('admin.plans.show', plan.id)}>
                <Eye className="mr-2 h-4 w-4" />
                View plan
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={route('admin.plans.edit', plan.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit plan
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={handleDelete}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete plan
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
