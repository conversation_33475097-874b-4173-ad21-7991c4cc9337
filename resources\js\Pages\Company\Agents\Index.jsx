import { Avatar, AvatarFallback, AvatarImage } from '@/Components/shadcn/ui/avatar'
import { Badge } from '@/Components/shadcn/ui/badge'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Input } from '@/Components/shadcn/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/shadcn/ui/select'
import AppLayout from '@/Layouts/AppLayout'
import { Icon } from '@iconify/react'
import { Head, Link, router } from '@inertiajs/react'
import { useState } from 'react'
import { route } from 'ziggy-js'

export default function AgentsIndex({ agents, filters }) {
  const [search, setSearch] = useState(filters.search || '')
  const [perPage, setPerPage] = useState(filters.per_page || 10)

  const handleSearch = (e) => {
    e.preventDefault()
    router.get(route('company.agents.index'), {
      search,
      per_page: perPage,
    }, {
      preserveState: true,
      replace: true,
    })
  }

  const resetFilters = () => {
    setSearch('')
    setPerPage(10)
    router.get(route('company.agents.index'), {}, {
      preserveState: true,
      replace: true,
    })
  }

  const getRoleBadge = (role) => {
    const variants = {
      agent: 'default',
      senior_agent: 'secondary',
      team_lead: 'destructive',
    }

    const labels = {
      agent: 'Agent',
      senior_agent: 'Senior Agent',
      team_lead: 'Team Lead',
    }

    return (
      <Badge variant={variants[role] || 'outline'}>
        {labels[role] || role}
      </Badge>
    )
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const handleDelete = (agent) => {
    // TODO: Replace with proper AlertDialog component
    // eslint-disable-next-line no-alert
    if (confirm(`Are you sure you want to remove ${agent.name} from the company?`)) {
      router.delete(route('company.agents.destroy', agent.id), {
        preserveScroll: true,
        onSuccess: () => {
          // Success message will be shown via flash message
        },
      })
    }
  }

  return (
    <AppLayout
      title="Company Agents"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Company Agents
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Manage your company agents and their roles
            </p>
          </div>
          <Link href={route('company.agents.create')}>
            <Button>
              <Icon icon="lucide:user-plus" className="mr-2 h-4 w-4" />
              Add New Agent
            </Button>
          </Link>
        </div>
      )}
    >
      <Head title="Company Agents" />

      <div className="py-12">
        <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
          <Card>
            <CardHeader>
              <CardTitle>Company Agents</CardTitle>
              <CardDescription>
                Manage agents in your company
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Search and Filters */}
              <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <form onSubmit={handleSearch} className="flex flex-1 gap-2">
                  <Input
                    type="text"
                    placeholder="Search agents by name, email, or license..."
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                    className="flex-1"
                  />
                  <Button type="submit">
                    <Icon icon="lucide:search" className="h-4 w-4" />
                  </Button>
                </form>

                <div className="flex items-center gap-2">
                  <Select value={perPage.toString()} onValueChange={value => setPerPage(Number.parseInt(value))}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={resetFilters}>
                    <Icon icon="lucide:x" className="mr-2 h-4 w-4" />
                    Reset
                  </Button>
                </div>
              </div>

              {/* Agents Table */}
              <div className="overflow-hidden rounded-lg border">
                <div className="overflow-x-auto">
                  <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                          Agent
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                          Role
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                          License
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                          Joined
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
                      {agents.data.length > 0
                        ? (
                            agents.data.map(agent => (
                              <tr key={agent.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <Avatar className="h-10 w-10">
                                      <AvatarImage src={agent.profile_photo_url} />
                                      <AvatarFallback>
                                        {agent.name.split(' ').map(n => n[0]).join('')}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="ml-4">
                                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                                        {agent.name}
                                      </div>
                                      <div className="text-sm text-gray-500 dark:text-gray-400">
                                        {agent.email}
                                      </div>
                                      {agent.phone && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                          {agent.phone}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  {getRoleBadge(agent.pivot?.role || 'agent')}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm text-gray-900 dark:text-white">
                                    {agent.license_number || 'Not provided'}
                                  </div>
                                  {agent.license_state && (
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                      {agent.license_state}
                                    </div>
                                  )}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {formatDate(agent.pivot?.created_at || agent.created_at)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                  <div className="flex items-center justify-end gap-2">
                                    <Link href={route('company.agents.show', agent.id)}>
                                      <Button variant="ghost" size="sm">
                                        <Icon icon="lucide:eye" className="h-4 w-4" />
                                      </Button>
                                    </Link>
                                    <Link href={route('company.agents.edit', agent.id)}>
                                      <Button variant="ghost" size="sm">
                                        <Icon icon="lucide:edit" className="h-4 w-4" />
                                      </Button>
                                    </Link>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleDelete(agent)}
                                      className="text-red-600 hover:text-red-800"
                                    >
                                      <Icon icon="lucide:trash-2" className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))
                          )
                        : (
                            <tr>
                              <td colSpan="5" className="px-6 py-12 text-center">
                                <div className="flex flex-col items-center">
                                  <Icon icon="lucide:users" className="h-12 w-12 text-gray-400" />
                                  <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                                    No agents found
                                  </h3>
                                  <p className="mt-1 text-sm text-gray-500">
                                    {search ? 'Try adjusting your search criteria.' : 'Start by adding your first agent.'}
                                  </p>
                                  {!search && (
                                    <div className="mt-6">
                                      <Link href={route('company.agents.create')}>
                                        <Button>
                                          <Icon icon="lucide:user-plus" className="mr-2 h-4 w-4" />
                                          Add First Agent
                                        </Button>
                                      </Link>
                                    </div>
                                  )}
                                </div>
                              </td>
                            </tr>
                          )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Pagination */}
              {agents.data.length > 0 && (
                <div className="mt-6 flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Showing
                    {' '}
                    {agents.from}
                    {' '}
                    to
                    {' '}
                    {agents.to}
                    {' '}
                    of
                    {' '}
                    {agents.total}
                    {' '}
                    results
                  </div>
                  <div className="flex items-center gap-2">
                    {agents.prev_page_url && (
                      <Link href={agents.prev_page_url}>
                        <Button variant="outline" size="sm">
                          <Icon icon="lucide:chevron-left" className="h-4 w-4" />
                          Previous
                        </Button>
                      </Link>
                    )}
                    {agents.next_page_url && (
                      <Link href={agents.next_page_url}>
                        <Button variant="outline" size="sm">
                          Next
                          <Icon icon="lucide:chevron-right" className="h-4 w-4" />
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  )
}
