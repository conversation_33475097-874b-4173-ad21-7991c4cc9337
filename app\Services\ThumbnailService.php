<?php

namespace App\Services;

use App\Models\Media;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ThumbnailService
{
    protected ImageManager $imageManager;
    protected array $config;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
        $this->config = config('media.thumbnails');
    }

    /**
     * Generate all thumbnail sizes for a media file.
     */
    public function generateThumbnails(Media $media): array
    {
        if (!$this->config['enabled'] || !$media->isImage()) {
            return [];
        }

        $generatedThumbnails = [];
        $originalPath = $media->getStoragePath();

        if (!Storage::disk($media->disk)->exists($originalPath)) {
            return [];
        }

        foreach ($this->config['sizes'] as $sizeName => $sizeConfig) {
            try {
                $thumbnailPath = $this->generateThumbnail($media, $sizeName, $sizeConfig);
                if ($thumbnailPath) {
                    $generatedThumbnails[$sizeName] = $thumbnailPath;
                }
            } catch (\Exception $e) {
                \Log::error("Failed to generate {$sizeName} thumbnail for media {$media->id}", [
                    'error' => $e->getMessage(),
                    'media_id' => $media->id,
                    'size' => $sizeName
                ]);
            }
        }

        return $generatedThumbnails;
    }

    /**
     * Generate a single thumbnail size.
     */
    public function generateThumbnail(Media $media, string $sizeName, array $sizeConfig): ?string
    {
        $originalPath = $media->getStoragePath();
        $thumbnailPath = $this->getThumbnailPath($media, $sizeName, $sizeConfig);

        // Skip if thumbnail already exists
        if (Storage::disk($media->disk)->exists($thumbnailPath)) {
            return $thumbnailPath;
        }

        try {
            // Read original image
            $originalContent = Storage::disk($media->disk)->get($originalPath);
            $image = $this->imageManager->read($originalContent);

            // Get dimensions
            $width = $sizeConfig['width'];
            $height = $sizeConfig['height'];
            $crop = $sizeConfig['crop'] ?? false;

            // Resize image
            if ($crop) {
                // Crop to exact dimensions (center crop)
                $image = $image->cover($width, $height);
            } else {
                // Resize maintaining aspect ratio
                $image = $image->scale($width, $height);
            }

            // Apply quality settings
            $quality = $this->config['quality'] ?? 85;

            // Encode image
            $encodedImage = $image->toJpeg($quality);

            // Save thumbnail
            Storage::disk($media->disk)->put($thumbnailPath, $encodedImage);

            \Log::info("Generated {$sizeName} thumbnail for media {$media->id}", [
                'media_id' => $media->id,
                'size' => $sizeName,
                'path' => $thumbnailPath,
                'dimensions' => "{$width}x{$height}"
            ]);

            return $thumbnailPath;

        } catch (\Exception $e) {
            \Log::error("Failed to generate thumbnail", [
                'error' => $e->getMessage(),
                'media_id' => $media->id,
                'size' => $sizeName,
                'original_path' => $originalPath,
                'thumbnail_path' => $thumbnailPath
            ]);

            return null;
        }
    }

    /**
     * Get the thumbnail path for a specific size (WordPress style).
     */
    public function getThumbnailPath(Media $media, string $sizeName, array $sizeConfig): string
    {
        $pathInfo = pathinfo($media->path);
        $directory = $pathInfo['dirname'] !== '.' ? $pathInfo['dirname'] . '/' : '';
        $filename = $pathInfo['filename'];
        $extension = $pathInfo['extension'];

        $width = $sizeConfig['width'];
        $height = $sizeConfig['height'];

        // WordPress style: image-150x150.jpg
        return $directory . $filename . '-' . $width . 'x' . $height . '.' . $extension;
    }

    /**
     * Get thumbnail URL for a specific size.
     */
    public function getThumbnailUrl(Media $media, ?string $sizeName = null): ?string
    {
        $sizeName = $sizeName ?: $this->config['default_size'];

        if (!isset($this->config['sizes'][$sizeName])) {
            return $media->url; // Return original if size doesn't exist
        }

        $sizeConfig = $this->config['sizes'][$sizeName];
        $thumbnailPath = $this->getThumbnailPath($media, $sizeName, $sizeConfig);

        // Check if thumbnail exists
        if (Storage::disk($media->disk)->exists($thumbnailPath)) {
            return Storage::disk($media->disk)->url($thumbnailPath);
        }

        // Try fallback sizes
        foreach ($this->config['fallback_sizes'] as $fallbackSize) {
            if ($fallbackSize === 'original') {
                return $media->url;
            }

            if (isset($this->config['sizes'][$fallbackSize])) {
                $fallbackConfig = $this->config['sizes'][$fallbackSize];
                $fallbackPath = $this->getThumbnailPath($media, $fallbackSize, $fallbackConfig);

                if (Storage::disk($media->disk)->exists($fallbackPath)) {
                    return Storage::disk($media->disk)->url($fallbackPath);
                }
            }
        }

        // Return original as final fallback
        return $media->url;
    }

    /**
     * Delete all thumbnails for a media file.
     */
    public function deleteThumbnails(Media $media): void
    {
        foreach ($this->config['sizes'] as $sizeName => $sizeConfig) {
            $thumbnailPath = $this->getThumbnailPath($media, $sizeName, $sizeConfig);

            if (Storage::disk($media->disk)->exists($thumbnailPath)) {
                Storage::disk($media->disk)->delete($thumbnailPath);

                \Log::info("Deleted {$sizeName} thumbnail for media {$media->id}", [
                    'media_id' => $media->id,
                    'size' => $sizeName,
                    'path' => $thumbnailPath
                ]);
            }
        }
    }

    /**
     * Check if a thumbnail exists for a specific size.
     */
    public function thumbnailExists(Media $media, string $sizeName): bool
    {
        if (!isset($this->config['sizes'][$sizeName])) {
            return false;
        }

        $sizeConfig = $this->config['sizes'][$sizeName];
        $thumbnailPath = $this->getThumbnailPath($media, $sizeName, $sizeConfig);

        return Storage::disk($media->disk)->exists($thumbnailPath);
    }

    /**
     * Get all available thumbnail sizes
     */
    public function getAvailableSizes(): array
    {
        return array_keys($this->config['sizes']);
    }

    /**
     * Get size configuration for a specific size
     */
    public function getSizeConfig(string $size): ?array
    {
        return $this->config['sizes'][$size] ?? null;
    }


}
