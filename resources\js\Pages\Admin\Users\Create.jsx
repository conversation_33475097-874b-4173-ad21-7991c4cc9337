import InputError from '@/Components/InputError'
import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/shadcn/ui/select'
import { Textarea } from '@/Components/shadcn/ui/textarea'
import AppLayout from '@/Layouts/AppLayout'
import { Icon } from '@iconify/react'
import { Head, Link, useForm } from '@inertiajs/react'
import { route } from 'ziggy-js'

export default function CreateUser({ userTypes }) {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    user_type: 'independent_agent',
    phone: '',
    company_name: '',
    company_address: '',
    license_number: '',
    license_state: '',
    license_expiry: '',
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    post(route('admin.users.store'))
  }

  const isBrokerageAdmin = data.user_type === 'brokerage_admin'

  return (
    <AppLayout
      title="Create User"
      renderHeader={() => (
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Create New User
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Add a new user to the system
            </p>
          </div>
          <Link href={route('admin.users.index')}>
            <Button variant="outline">
              <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
              Back to Users
            </Button>
          </Link>
        </div>
      )}
    >
      <Head title="Create User" />

      <div className="py-12">
        <div className="mx-auto max-w-4xl sm:px-6 lg:px-8">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>Enter user's basic details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="name">Full Name *</Label>
                      <Input
                        id="name"
                        type="text"
                        value={data.name}
                        onChange={e => setData('name', e.target.value)}
                        required
                        placeholder="John Doe"
                      />
                      <InputError message={errors.name} />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={data.email}
                        onChange={e => setData('email', e.target.value)}
                        required
                        placeholder="<EMAIL>"
                      />
                      <InputError message={errors.email} />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="password">Password *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={data.password}
                        onChange={e => setData('password', e.target.value)}
                        required
                        placeholder="••••••••"
                      />
                      <InputError message={errors.password} />
                    </div>
                    <div>
                      <Label htmlFor="password_confirmation">Confirm Password *</Label>
                      <Input
                        id="password_confirmation"
                        type="password"
                        value={data.password_confirmation}
                        onChange={e => setData('password_confirmation', e.target.value)}
                        required
                        placeholder="••••••••"
                      />
                      <InputError message={errors.password_confirmation} />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <Label htmlFor="user_type">User Type *</Label>
                      <Select value={data.user_type} onValueChange={value => setData('user_type', value)}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select user type" />
                        </SelectTrigger>
                        <SelectContent>
                          {userTypes.map(type => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <InputError message={errors.user_type} />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={data.phone}
                        onChange={e => setData('phone', e.target.value)}
                        placeholder="(*************"
                      />
                      <InputError message={errors.phone} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Information (for Brokerage Admins) */}
              {isBrokerageAdmin && (
                <Card>
                  <CardHeader>
                    <CardTitle>Company Information</CardTitle>
                    <CardDescription>Company details for company administrators</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="company_name">Company Name</Label>
                      <Input
                        id="company_name"
                        type="text"
                        value={data.company_name}
                        onChange={e => setData('company_name', e.target.value)}
                        placeholder="ABC Real Estate"
                      />
                      <InputError message={errors.company_name} />
                    </div>
                    <div>
                      <Label htmlFor="company_address">Company Address</Label>
                      <Textarea
                        id="company_address"
                        value={data.company_address}
                        onChange={e => setData('company_address', e.target.value)}
                        placeholder="123 Main St, City, State 12345"
                        rows={3}
                      />
                      <InputError message={errors.company_address} />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* License Information */}
              <Card>
                <CardHeader>
                  <CardTitle>License Information</CardTitle>
                  <CardDescription>Real estate license details (optional)</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                      <Label htmlFor="license_number">License Number</Label>
                      <Input
                        id="license_number"
                        type="text"
                        value={data.license_number}
                        onChange={e => setData('license_number', e.target.value)}
                        placeholder="RE123456"
                      />
                      <InputError message={errors.license_number} />
                    </div>
                    <div>
                      <Label htmlFor="license_state">License State</Label>
                      <Input
                        id="license_state"
                        type="text"
                        value={data.license_state}
                        onChange={e => setData('license_state', e.target.value)}
                        placeholder="CA"
                        maxLength={2}
                      />
                      <InputError message={errors.license_state} />
                    </div>
                    <div>
                      <Label htmlFor="license_expiry">License Expiry</Label>
                      <Input
                        id="license_expiry"
                        type="date"
                        value={data.license_expiry}
                        onChange={e => setData('license_expiry', e.target.value)}
                      />
                      <InputError message={errors.license_expiry} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-end space-x-4">
                    <Link href={route('admin.users.index')}>
                      <Button type="button" variant="outline">
                        Cancel
                      </Button>
                    </Link>
                    <Button type="submit" disabled={processing}>
                      {processing
                        ? (
                            <>
                              <Icon icon="lucide:loader-2" className="mr-2 h-4 w-4 animate-spin" />
                              Creating...
                            </>
                          )
                        : (
                            <>
                              <Icon icon="lucide:plus" className="mr-2 h-4 w-4" />
                              Create User
                            </>
                          )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </form>
        </div>
      </div>
    </AppLayout>
  )
}
