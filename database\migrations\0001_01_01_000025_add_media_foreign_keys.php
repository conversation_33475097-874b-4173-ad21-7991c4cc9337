<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraints after all tables are created
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('profile_image_id')->references('id')->on('media')->nullOnDelete();
            $table->foreign('parent_company_id')->references('id')->on('teams')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['profile_image_id']);
            $table->dropForeign(['parent_company_id']);
        });
    }
};
