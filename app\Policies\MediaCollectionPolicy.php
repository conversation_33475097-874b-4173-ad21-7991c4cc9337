<?php

namespace App\Policies;

use App\Models\User;
use App\Models\MediaCollection;

class MediaCollectionPolicy
{
    /**
     * Determine whether the user can view any media collections.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view collections they have access to
    }

    /**
     * Determine whether the user can view the media collection.
     */
    public function view(User $user, MediaCollection $collection): bool
    {
        return $collection->canBeAccessedBy($user);
    }

    /**
     * Determine whether the user can create media collections.
     */
    public function create(User $user): bool
    {
        return $user->can_upload_media;
    }

    /**
     * Determine whether the user can update the media collection.
     */
    public function update(User $user, MediaCollection $collection): bool
    {
        // Super admin can update any collection
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        // Creator can update their own collection
        if ($collection->created_by === $user->id) {
            return true;
        }

        // Company admin can update collections from their company
        if ($user->user_type->canManageCompanyMedia() &&
            $collection->company_id === $user->currentTeam?->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the media collection.
     */
    public function delete(User $user, MediaCollection $collection): bool
    {
        return $this->update($user, $collection);
    }

    /**
     * Determine whether the user can restore the media collection.
     */
    public function restore(User $user, MediaCollection $collection): bool
    {
        return $this->delete($user, $collection);
    }

    /**
     * Determine whether the user can permanently delete the media collection.
     */
    public function forceDelete(User $user, MediaCollection $collection): bool
    {
        return $user->user_type->canAccessAllMedia();
    }

    /**
     * Determine whether the user can add media to the collection.
     */
    public function addMedia(User $user, MediaCollection $collection): bool
    {
        return $this->update($user, $collection);
    }

    /**
     * Determine whether the user can remove media from the collection.
     */
    public function removeMedia(User $user, MediaCollection $collection): bool
    {
        return $this->update($user, $collection);
    }
}
