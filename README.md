# Larasonic 🚀

![<PERSON><PERSON>](public/images/og.webp)

Larasonic is a modern, production-ready SaaS starter kit built on the **RILT stack** (React, Inertia, Laravel, TailwindCSS). It provides everything you need to build scalable SaaS applications with a focus on developer experience and code quality.

![GitHub Repo stars](https://img.shields.io/github/stars/pushpak1300/Larasonic?style=for-the-badge) [![Licence](https://img.shields.io/github/license/Ileriayo/markdown-badges?style=for-the-badge)](./LICENSE.md) [![Github-sponsors](https://img.shields.io/badge/sponsor-30363D?style=for-the-badge&logo=GitHub-Sponsors&logoColor=#EA4AAA)](https://github.com/sponsors/pushpak1300)

## 🏗️ Architecture

Larasonic combines the best of modern web development:

- **Laravel 11** - Robust PHP backend with strict typing
- **React 19** - Modern frontend with latest features
- **Inertia.js 2.0** - SPA experience without API complexity
- **TailwindCSS 4+** - Utility-first styling
- **shadcn/ui** - High-quality component library
- **TypeScript Ready** - Structured for future TS adoption

## ✨ Features

- ⚡ **10x Dev Experience** - Laravel Pint, PHPStan, Rector, ESLint
- 🐳 **Production Docker Ready** - Optimized with Laravel Octane
- 🔑 **Advanced Authentication** - Jetstream + Sanctum + Social login
- 💳 **Payment Ready** - Laravel Cashier (Stripe) integration
- 🌐 **API Ready** - RESTful endpoints with Sanctum auth
- 🎨 **Customizable UI** - shadcn/ui components + TailwindCSS
- 🧠 **AI Integration Ready** - Pre-configured LLM integrations
- 📊 **FilamentPHP Admin** - Beautiful admin panel
- 🔒 **Security First** - CSRF protection, validation, authorization
- 📱 **Responsive Design** - Mobile-first approach
- 🚀 **Performance Optimized** - Code splitting, lazy loading, caching

## 🚀 Quick Start

### Prerequisites

- PHP 8.3+
- Node.js 18+
- Composer
- Docker (optional but recommended)

### Option 1: Docker (Recommended)

```bash
# Clone the repository
<NAME_EMAIL>:shipfastlabs/larasonic-react.git
cd larasonic-react

# Install PHP dependencies
docker run --rm \
    -u "$(id -u):$(id -g)" \
    -v "$(pwd):/var/www/html" \
    -w /var/www/html \
    laravelsail/php83-composer:latest \
    composer install --ignore-platform-reqs

# Setup environment and start services
cp .env.example .env
./vendor/bin/sail up -d
./vendor/bin/sail composer run setup
```

### Option 2: Local Development

```bash
# Clone and install dependencies
<NAME_EMAIL>:shipfastlabs/larasonic-react.git
cd larasonic-react
composer install
npm install

# Setup environment
cp .env.example .env
composer run setup
```

### Start Development

```bash
# Start all development services (Laravel + Vite + Queue + Logs)
composer run dev
```

Visit `http://localhost:8000` to see your application!

## 📚 Documentation

- **[Architecture Guide](ARCHITECTURE.md)** - Understanding the RILT stack
- **[Development Guide](DEVELOPMENT_GUIDE.md)** - Building with Laravel + Inertia.js + React
- **[Inertia.js Guide](INERTIA_REACT_GUIDE.md)** - Mastering the bridge between Laravel and React
- **[Official Docs](https://docs.larasonic.com)** - Complete documentation

## 🏗️ Project Structure

```
├── app/
│   ├── Http/Controllers/     # Laravel controllers
│   ├── Models/              # Eloquent models
│   ├── Enums/               # PHP enums for type safety
│   └── ...
├── resources/
│   ├── js/
│   │   ├── Components/      # Reusable React components
│   │   ├── Layouts/         # Layout components
│   │   ├── Pages/           # Page components (map to routes)
│   │   └── app.jsx          # React app entry point
│   └── views/app.blade.php  # Main HTML template
├── routes/web.php           # Laravel routes
└── ...
```

## 🔄 How It Works

1. **Laravel Routes** define your application endpoints
2. **Controllers** return `Inertia::render()` with React component name + data
3. **Inertia.js** bridges Laravel and React seamlessly
4. **React Components** receive Laravel data as props
5. **No API needed** - direct data flow from backend to frontend

```php
// Laravel Controller
return Inertia::render('Dashboard', [
    'user' => auth()->user(),
    'stats' => $dashboardStats
]);
```

```jsx
// React Component
export default function Dashboard({ user, stats }) {
  return (
    <AppLayout title="Dashboard">
      <h1>
        Welcome
        {user.name}
      </h1>
      <StatsGrid stats={stats} />
    </AppLayout>
  )
}
```

## 🛠️ Development Workflow

### Code Quality Tools

```bash
./vendor/bin/pint          # Format PHP code
./vendor/bin/phpstan       # Static analysis
./vendor/bin/rector        # Modernize code
npm run lint:fix           # Fix JS/React issues
```

### Testing

```bash
./vendor/bin/pest          # Run PHP tests
npm run test               # Run frontend tests (when added)
```

## 🚀 Deployment

### Production Build

```bash
npm run build              # Build frontend assets
php artisan optimize       # Optimize Laravel
```

### Docker Production

```bash
docker build -t larasonic:latest .
docker run -p 80:8000 larasonic:latest
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 🔒 Security

Report <NAME_EMAIL>

## 📄 License

[MIT](https://opensource.org/licenses/MIT)

## 🙏 Acknowledgments

Proudly hosted and sponsored by [Sevalla.com](https://sevalla.com/?ref=larasonic).

## Screenshots

| ![Screenshot 4](https://github.com/user-attachments/assets/d7c4eaa9-b547-4952-8ade-4b0ae62aee0e) | ![Screenshot 2](https://github.com/user-attachments/assets/b2d5a28c-9b1b-40bb-82f0-fb9fa932165c) | ![Screenshot 3](https://github.com/user-attachments/assets/d8b15834-bcc2-4028-9d73-a0bb9983c6b7) |
| :----------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------: | :----------------------------------------------------------------------------------------------: |
| ![Screenshot 1](https://github.com/user-attachments/assets/21c34465-a193-4373-9862-0843f11b957c) | ![Screenshot 5](https://github.com/user-attachments/assets/fba2d341-40c3-4244-8b02-82891c42f2d5) | ![Screenshot 6](https://github.com/user-attachments/assets/37ce7a37-121d-41b1-b3e6-09714cb5c884) |
