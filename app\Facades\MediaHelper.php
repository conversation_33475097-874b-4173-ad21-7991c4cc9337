<?php

declare(strict_types=1);

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static string|null getUrl(?int $mediaId)
 * @method static string|null getThumbnailUrl(?int $mediaId, ?string $size = null)
 * @method static string|null getFullUrl(?int $mediaId)
 * @method static \App\Models\Media|null getMedia(?int $mediaId)
 * @method static \App\Models\Media|null getMediaWithMetadata(?int $mediaId)
 * @method static string|null getAltText(?int $mediaId)
 * @method static string|null getTitle(?int $mediaId)
 * @method static bool canAccess(?int $mediaId, $user)
 * @method static array|null getDimensions(?int $mediaId)
 * @method static string|null getFormattedSize(?int $mediaId)
 * @method static bool isImage(?int $mediaId)
 * @method static bool isVideo(?int $mediaId)
 * @method static void clearCache(int $mediaId)
 * @method static string|null getProfileImageUrl(?int $mediaId, ?string $fallbackPath = null)
 * @method static string|null getProfileThumbnailUrl(?int $mediaId, ?string $fallbackPath = null, ?string $size = null)
 */
class MediaHelper extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return \App\Services\MediaHelper::class;
    }
}
