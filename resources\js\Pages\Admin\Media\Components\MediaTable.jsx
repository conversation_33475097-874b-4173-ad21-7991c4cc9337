import React, { useState } from 'react';
import { router } from '@inertiajs/react';
import { format } from 'date-fns';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/shadcn/ui/table';
import { Checkbox } from '@/components/shadcn/ui/checkbox';
import { Badge } from '@/components/shadcn/ui/badge';
import { Button } from '@/components/shadcn/ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
    DropdownMenuItem,
    DropdownMenuSeparator
} from '@/components/shadcn/ui/dropdown-menu';
import {
    FileImage,
    FileText,
    FileVideo,
    FileAudio,
    File,
    MoreHorizontal,
    Eye,
    Download,
    Copy,
    Trash2
} from 'lucide-react';

import MediaDetailsModal from './MediaDetailsModal';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/shadcn/ui/pagination';

export default function MediaTable({
    media,
    selectedMedia,
    onMediaSelect,
    onSelectAll,
    userPermissions
}) {
    const [selectedMediaItem, setSelectedMediaItem] = useState(null);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    // Get file icon based on type
    const getFileIcon = (mediaItem) => {
        const iconClass = "h-4 w-4";

        if (mediaItem.file_type === 'image') {
            return <FileImage className={iconClass} />;
        } else if (mediaItem.file_type === 'video') {
            return <FileVideo className={iconClass} />;
        } else if (mediaItem.file_type === 'audio') {
            return <FileAudio className={iconClass} />;
        } else if (mediaItem.file_type === 'document') {
            return <FileText className={iconClass} />;
        }

        return <File className={iconClass} />;
    };

    // Handle media actions
    const handleViewDetails = (mediaItem) => {
        setSelectedMediaItem(mediaItem);
        setShowDetailsModal(true);
    };

    const handleDownload = (mediaItem) => {
        window.open(mediaItem.url, '_blank');
    };

    const handleCopyUrl = async (mediaItem) => {
        try {
            await navigator.clipboard.writeText(mediaItem.full_url);
            // You could add a toast notification here
        } catch (err) {
            console.error('Failed to copy URL:', err);
        }
    };

    const handleDelete = async (mediaItem) => {
        if (!confirm(`Are you sure you want to delete "${mediaItem.original_filename}"?`)) {
            return;
        }

        try {
            await router.delete(route('admin.media.destroy', mediaItem.id));
        } catch (error) {
            console.error('Failed to delete media:', error);
        }
    };

    if (media.data.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center py-12">
                <File className="h-12 w-12 text-muted-foreground/50 mb-4" />
                <p className="text-muted-foreground font-medium">No media files found</p>
                <p className="text-sm text-muted-foreground">Upload some files to get started</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Table */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-12">
                                <Checkbox
                                    checked={selectedMedia.length === media.data.length}
                                    onCheckedChange={onSelectAll}
                                    aria-label="Select all media"
                                />
                            </TableHead>
                            <TableHead className="w-16">Preview</TableHead>
                            <TableHead>Name</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Size</TableHead>
                            <TableHead>Uploaded By</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead className="w-12">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {media.data.map((mediaItem) => (
                            <TableRow key={mediaItem.id}>
                                <TableCell>
                                    <Checkbox
                                        checked={selectedMedia.includes(mediaItem.id)}
                                        onCheckedChange={() => onMediaSelect(mediaItem.id)}
                                    />
                                </TableCell>

                                <TableCell>
                                    <div className="h-10 w-10 bg-muted rounded flex items-center justify-center overflow-hidden">
                                        {mediaItem.file_type === 'image' && mediaItem.thumbnail_url ? (
                                            <img
                                                src={mediaItem.thumbnail_url}
                                                alt={mediaItem.alt_text || mediaItem.original_filename}
                                                className="h-full w-full object-cover rounded"
                                                onError={(e) => {
                                                    console.error('Thumbnail failed to load, trying original:', mediaItem.thumbnail_url);
                                                    // Fallback to original image
                                                    e.target.src = mediaItem.url;
                                                    e.target.onerror = (fallbackE) => {
                                                        console.error('Original image also failed to load:', mediaItem.url);
                                                        fallbackE.target.style.display = 'none';
                                                        fallbackE.target.parentElement.innerHTML = getFileIcon(mediaItem);
                                                    };
                                                }}
                                            />
                                        ) : (
                                            getFileIcon(mediaItem)
                                        )}
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <div className="space-y-1">
                                        <button
                                            onClick={() => handleViewDetails(mediaItem)}
                                            className="font-medium hover:underline text-left"
                                        >
                                            {mediaItem.title || mediaItem.original_filename}
                                        </button>
                                        {mediaItem.description && (
                                            <p className="text-xs text-muted-foreground line-clamp-1">
                                                {mediaItem.description}
                                            </p>
                                        )}
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <Badge variant="outline" className="flex items-center gap-1 w-fit">
                                        {getFileIcon(mediaItem)}
                                        <span>{mediaItem.extension?.toUpperCase() || 'FILE'}</span>
                                    </Badge>
                                </TableCell>

                                <TableCell className="text-muted-foreground">
                                    {mediaItem.formatted_size}
                                </TableCell>

                                <TableCell className="text-muted-foreground">
                                    {mediaItem.uploader?.name || 'Unknown'}
                                </TableCell>

                                <TableCell className="text-muted-foreground">
                                    {format(new Date(mediaItem.created_at), 'MMM d, yyyy')}
                                </TableCell>

                                <TableCell>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => handleViewDetails(mediaItem)}>
                                                <Eye className="h-4 w-4 mr-2" />
                                                View Details
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleDownload(mediaItem)}>
                                                <Download className="h-4 w-4 mr-2" />
                                                Download
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleCopyUrl(mediaItem)}>
                                                <Copy className="h-4 w-4 mr-2" />
                                                Copy URL
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem
                                                onClick={() => handleDelete(mediaItem)}
                                                className="text-destructive"
                                            >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {media.last_page > 1 && (
                <div className="flex justify-center">
                    <Pagination>
                        <PaginationContent>
                            {media.current_page > 1 && (
                                <PaginationItem>
                                    <PaginationPrevious
                                        href={route('admin.media.index', { ...route().params, page: media.current_page - 1 })}
                                    />
                                </PaginationItem>
                            )}

                            {Array.from({ length: Math.min(5, media.last_page) }, (_, i) => {
                                const page = i + 1;
                                return (
                                    <PaginationItem key={page}>
                                        <PaginationLink
                                            href={route('admin.media.index', { ...route().params, page })}
                                            isActive={page === media.current_page}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            })}

                            {media.current_page < media.last_page && (
                                <PaginationItem>
                                    <PaginationNext
                                        href={route('admin.media.index', { ...route().params, page: media.current_page + 1 })}
                                    />
                                </PaginationItem>
                            )}
                        </PaginationContent>
                    </Pagination>
                </div>
            )}

            {/* Details Modal */}
            {showDetailsModal && selectedMediaItem && (
                <MediaDetailsModal
                    isOpen={showDetailsModal}
                    onClose={() => {
                        setShowDetailsModal(false);
                        setSelectedMediaItem(null);
                    }}
                    media={selectedMediaItem}
                    onUpdate={(updatedMedia) => {
                        // Update the media in the list
                        router.reload({ only: ['media'] });
                    }}
                    userPermissions={userPermissions}
                />
            )}
        </div>
    );
}
