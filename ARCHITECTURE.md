# 🏗️ Larasonic Architecture Guide

## Overview

Larasonic is a modern SaaS starter kit built on the **RILT stack** (React, Inertia, Laravel, TailwindCSS). This document explains the architectural decisions and patterns used throughout the project.

## Technology Stack

### Backend

- **<PERSON>vel 11** - PHP framework with modern features
- **PHP 8.3+** - Latest PHP with strict typing and enums
- **Laravel Jetstream** - Authentication scaffolding
- **Laravel Sanctum** - API authentication
- **Laravel Cashier** - Stripe payment integration
- **FilamentPHP** - Admin panel
- **Laravel Octane** - High-performance application server

### Frontend

- **React 19** - Modern React with latest features
- **Inertia.js 2.0** - SPA-like experience without API complexity
- **TailwindCSS 4+** - Utility-first CSS framework
- **shadcn/ui** - High-quality React components
- **Vite 6+** - Fast build tool and dev server

### Development Tools

- **Laravel Pint** - Code formatting
- **PHPStan** - Static analysis (maximum level)
- **Rector** - Code modernization
- **ESLint** - JavaScript/React linting
- **Pest** - Testing framework

## Core Architecture Patterns

### 1. Inertia.js Bridge Pattern

Inertia.js eliminates the need for separate API endpoints by allowing Laravel controllers to return React components directly:

```php
// Laravel Controller
public function dashboard(): Response
{
    return Inertia::render('Dashboard', [
        'user' => auth()->user(),
        'stats' => $this->getDashboardStats()
    ]);
}
```

```jsx
// React Component
export default function Dashboard({ user, stats }) {
  return (
    <AppLayout title="Dashboard">
      <h1>
        Welcome
        {user.name}
      </h1>
      <StatsGrid stats={stats} />
    </AppLayout>
  )
}
```

### 2. Layout Composition Pattern

The project uses a hierarchical layout system:

```
WebLayout (Public pages)
├── Header with navigation
├── Main content area
└── Footer

AppLayout (Authenticated pages)
├── Sidebar navigation
├── Header with breadcrumbs
└── Main content area
```

### 3. Component-Based Architecture

```
resources/js/
├── Components/           # Reusable UI components
│   ├── shadcn/          # shadcn/ui components
│   ├── hooks/           # Custom React hooks
│   └── lib/             # Utility functions
├── Layouts/             # Layout components
├── Pages/               # Page components (map to routes)
├── Composables/         # Vue-style composables for React
└── app.jsx             # Application entry point
```

### 4. Type-Safe User Management

The project uses PHP enums for type safety:

```php
enum UserType: string
{
    case PLATFORM_ADMINISTRATOR = 'platform_administrator';
    case BROKERAGE_ADMIN = 'brokerage_admin';
    case COMPANY_AGENT = 'company_agent';
    case INDEPENDENT_AGENT = 'independent_agent';

    public function canManageTeams(): bool
    {
        return $this === self::BROKERAGE_ADMIN || $this === self::PLATFORM_ADMINISTRATOR;
    }
}
```

## Data Flow Architecture

### Request Lifecycle

1. **Browser Request** → Laravel Route
2. **Controller** → Business Logic + Data Preparation
3. **Inertia::render()** → Component Name + Props
4. **HandleInertiaRequests Middleware** → Shared Data Injection
5. **React Component** → UI Rendering
6. **User Interaction** → Inertia Form/Link → Back to Step 1

### Shared Data Pattern

The `HandleInertiaRequests` middleware provides global data:

```php
public function share(Request $request): array
{
    return [
        'auth' => [
            'user' => $request->user()?->load('teams'),
        ],
        'flash' => [
            'success' => $request->session()->get('success'),
            'error' => $request->session()->get('error'),
        ],
        'name' => config('app.name'),
    ];
}
```

Access in React:

```jsx
import { usePage } from '@inertiajs/react'

export default function Component() {
  const { props } = usePage()
  const user = props.auth.user
  const flashMessage = props.flash.success

  return <div>...</div>
}
```

## Security Architecture

### Authentication Flow

1. **Laravel Jetstream** handles registration/login
2. **Laravel Sanctum** provides session-based auth
3. **Inertia.js** automatically includes CSRF tokens
4. **Role-based access** via user types and permissions

### Authorization Patterns

```php
// Controller level
Route::middleware(['auth', 'can:access-admin'])->group(function () {
    Route::resource('admin/users', AdminUserController::class);
});

// Model level
public function canAccessAdmin(): bool
{
    return $this->isPlatformAdministrator();
}
```

## Performance Optimizations

### Frontend

- **Code Splitting** - Automatic route-based splitting
- **Lazy Loading** - Components loaded on demand
- **Asset Optimization** - Vite handles bundling and minification
- **Hot Module Replacement** - Fast development feedback

### Backend

- **Laravel Octane** - Persistent application state
- **Database Optimization** - Eager loading, proper indexing
- **Caching Strategy** - Redis for sessions and cache
- **Queue System** - Background job processing

## Deployment Architecture

### Docker Setup

```dockerfile
# Multi-stage build for production
FROM php:8.3-fpm-alpine AS production
# Optimized for Laravel Octane
```

### Environment Configuration

- **Development** - Laravel Sail with Docker
- **Production** - Optimized Docker images
- **CI/CD** - GitHub Actions for testing and deployment

## Scalability Considerations

### Horizontal Scaling

- **Stateless Application** - Session data in Redis
- **Load Balancer Ready** - No server-specific state
- **Database Separation** - Read/write splitting capability

### Vertical Scaling

- **Laravel Octane** - Better resource utilization
- **Optimized Queries** - N+1 prevention
- **Caching Layers** - Multiple cache strategies

## Development Workflow

### Code Quality

```bash
# Format code
./vendor/bin/pint

# Static analysis
./vendor/bin/phpstan analyse

# Modernize code
./vendor/bin/rector

# Frontend linting
npm run lint:fix
```

### Testing Strategy

```bash
# Backend tests
./vendor/bin/pest

# Frontend tests (when added)
npm run test
```

### Development Commands

```bash
# Start development environment
composer run dev

# This runs:
# - php artisan serve (Laravel server)
# - php artisan queue:listen (Queue worker)
# - php artisan pail (Log viewer)
# - npm run dev (Vite dev server)
```

## Best Practices

### Laravel + Inertia.js

1. **Keep controllers thin** - Move logic to services/actions
2. **Use form requests** - Validate data properly
3. **Leverage shared data** - Avoid prop drilling
4. **Handle errors gracefully** - Use Inertia's error handling

### React Components

1. **Composition over inheritance** - Use layout composition
2. **Custom hooks** - Extract reusable logic
3. **TypeScript-ready** - Structure for future TS adoption
4. **Accessibility first** - Use semantic HTML and ARIA

### Performance

1. **Lazy load routes** - Split code by pages
2. **Optimize images** - Use appropriate formats
3. **Cache strategically** - Both frontend and backend
4. **Monitor performance** - Use Laravel Telescope

This architecture provides a solid foundation for building scalable SaaS applications while maintaining developer productivity and code quality.
