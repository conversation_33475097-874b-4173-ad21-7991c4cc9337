import { Button } from '@/Components/shadcn/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/shadcn/ui/card'
import { Checkbox } from '@/Components/shadcn/ui/checkbox'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/shadcn/ui/select'
import { Textarea } from '@/Components/shadcn/ui/textarea'
import AppLayout from '@/Layouts/AppLayout'
import { Icon } from '@iconify/react'
import { Head, Link, useForm } from '@inertiajs/react'
import { route } from 'ziggy-js'

export default function CreatePlan() {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    description: '',
    price: '',
    billing_period: 'monthly',
    stripe_price_id: '',
    features: [''],
    is_active: true,
    is_popular: false,
    max_agents: '',
    max_listings: '',
    sort_order: 0,
  })

  const handleSubmit = (e) => {
    e.preventDefault()

    // Filter out empty features
    const filteredFeatures = data.features.filter(feature => feature.trim() !== '')

    post(route('admin.plans.store'), {
      ...data,
      features: filteredFeatures,
    })
  }

  const addFeature = () => {
    setData('features', [...data.features, ''])
  }

  const removeFeature = (index) => {
    const newFeatures = data.features.filter((_, i) => i !== index)
    setData('features', newFeatures)
  }

  const updateFeature = (index, value) => {
    const newFeatures = [...data.features]
    newFeatures[index] = value
    setData('features', newFeatures)
  }

  return (
    <AppLayout title="Create Plan">
      <Head title="Create Plan" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Create New Plan</h1>
            <p className="text-muted-foreground">
              Add a new subscription plan to your platform
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href={route('admin.plans.index')}>
              <Icon icon="lucide:arrow-left" className="mr-2 h-4 w-4" />
              Back to Plans
            </Link>
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Plan Name *</Label>
                  <Input
                    id="name"
                    value={data.name}
                    onChange={e => setData('name', e.target.value)}
                    placeholder="e.g., Basic Plan"
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive mt-1">{errors.name}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={data.description}
                    onChange={e => setData('description', e.target.value)}
                    placeholder="Describe what this plan offers..."
                    className={errors.description ? 'border-destructive' : ''}
                  />
                  {errors.description && (
                    <p className="text-sm text-destructive mt-1">{errors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="price">Price *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={data.price}
                      onChange={e => setData('price', e.target.value)}
                      placeholder="0.00"
                      className={errors.price ? 'border-destructive' : ''}
                    />
                    {errors.price && (
                      <p className="text-sm text-destructive mt-1">{errors.price}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="billing_period">Billing Period *</Label>
                    <Select
                      value={data.billing_period}
                      onValueChange={value => setData('billing_period', value)}
                    >
                      <SelectTrigger className={errors.billing_period ? 'border-destructive' : ''}>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.billing_period && (
                      <p className="text-sm text-destructive mt-1">{errors.billing_period}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="stripe_price_id">Stripe Price ID</Label>
                  <Input
                    id="stripe_price_id"
                    value={data.stripe_price_id}
                    onChange={e => setData('stripe_price_id', e.target.value)}
                    placeholder="price_1234567890"
                    className={errors.stripe_price_id ? 'border-destructive' : ''}
                  />
                  {errors.stripe_price_id && (
                    <p className="text-sm text-destructive mt-1">{errors.stripe_price_id}</p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Leave empty if not using Stripe integration
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Settings & Limits */}
            <Card>
              <CardHeader>
                <CardTitle>Settings & Limits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="max_agents">Max Agents</Label>
                    <Input
                      id="max_agents"
                      type="number"
                      min="1"
                      value={data.max_agents}
                      onChange={e => setData('max_agents', e.target.value)}
                      placeholder="Unlimited"
                      className={errors.max_agents ? 'border-destructive' : ''}
                    />
                    {errors.max_agents && (
                      <p className="text-sm text-destructive mt-1">{errors.max_agents}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="max_listings">Max Listings</Label>
                    <Input
                      id="max_listings"
                      type="number"
                      min="1"
                      value={data.max_listings}
                      onChange={e => setData('max_listings', e.target.value)}
                      placeholder="Unlimited"
                      className={errors.max_listings ? 'border-destructive' : ''}
                    />
                    {errors.max_listings && (
                      <p className="text-sm text-destructive mt-1">{errors.max_listings}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="sort_order">Sort Order</Label>
                  <Input
                    id="sort_order"
                    type="number"
                    min="0"
                    value={data.sort_order}
                    onChange={e => setData('sort_order', e.target.value)}
                    className={errors.sort_order ? 'border-destructive' : ''}
                  />
                  {errors.sort_order && (
                    <p className="text-sm text-destructive mt-1">{errors.sort_order}</p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Lower numbers appear first
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_active"
                      checked={data.is_active}
                      onCheckedChange={checked => setData('is_active', checked)}
                    />
                    <Label htmlFor="is_active">Active Plan</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="is_popular"
                      checked={data.is_popular}
                      onCheckedChange={checked => setData('is_popular', checked)}
                    />
                    <Label htmlFor="is_popular">Popular Plan</Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle>Plan Features</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {data.features.map((feature, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={feature}
                    onChange={e => updateFeature(index, e.target.value)}
                    placeholder="Enter feature description..."
                    className="flex-1"
                  />
                  {data.features.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => removeFeature(index)}
                    >
                      <Icon icon="lucide:trash" className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addFeature}
                className="w-full"
              >
                <Icon icon="lucide:plus" className="mr-2 h-4 w-4" />
                Add Feature
              </Button>

              {errors.features && (
                <p className="text-sm text-destructive">{errors.features}</p>
              )}
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" asChild>
              <Link href={route('admin.plans.index')}>Cancel</Link>
            </Button>
            <Button type="submit" disabled={processing}>
              {processing && <Icon icon="lucide:loader-2" className="mr-2 h-4 w-4 animate-spin" />}
              Create Plan
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  )
}
