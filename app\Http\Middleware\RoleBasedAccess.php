<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\UserType;

class RoleBasedAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        $user = $request->user();

        if (!$user) {
            abort(401, 'Unauthenticated');
        }

        // Check if user has any of the required roles
        $hasRole = false;
        foreach ($roles as $role) {
            if ($this->userHasRole($user, $role)) {
                $hasRole = true;
                break;
            }
        }

        if (!$hasRole) {
            abort(403, 'Insufficient permissions');
        }

        return $next($request);
    }

    /**
     * Check if user has the specified role.
     */
    private function userHasRole($user, string $role): bool
    {
        // Check UserType enum first
        if ($user->user_type->value === $role) {
            return true;
        }

        // Check Spatie role
        if ($user->hasRole($role)) {
            return true;
        }

        return false;
    }
}
