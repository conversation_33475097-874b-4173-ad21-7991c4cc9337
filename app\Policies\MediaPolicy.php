<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Media;
use App\Enums\UserType;

class MediaPolicy
{
    /**
     * Determine whether the user can view any media.
     */
    public function viewAny(User $user): bool
    {
        return $user->can_upload_media || $user->user_type->canAccessAllMedia();
    }

    /**
     * Determine whether the user can view the media.
     */
    public function view(User $user, Media $media): bool
    {
        return $media->canBeAccessedBy($user);
    }

    /**
     * Determine whether the user can create media.
     */
    public function create(User $user): bool
    {
        \Log::info('MediaPolicy::create check', [
            'user_id' => $user->id,
            'user_type' => $user->user_type->value,
            'user_current_team_id' => $user->currentTeam?->id,
            'canAccessAllMedia' => $user->user_type->canAccessAllMedia(),
            'canManageCompanyMedia' => $user->user_type->canManageCompanyMedia(),
            'can_upload_media' => $user->can_upload_media,
        ]);

        // Super admin can always create
        if ($user->user_type->canAccessAllMedia()) {
            \Log::info('MediaPolicy::create - ALLOWED: Super admin');
            return true;
        }

        // Company admin can always create
        if ($user->user_type->canManageCompanyMedia()) {
            \Log::info('MediaPolicy::create - ALLOWED: Company admin');
            return true;
        }

        // Individual/child agents need explicit permission
        if ($user->can_upload_media) {
            \Log::info('MediaPolicy::create - ALLOWED: User has upload permission');
            return true;
        }

        \Log::info('MediaPolicy::create - DENIED: No matching conditions');
        return false;
    }

    /**
     * Determine whether the user can update the media.
     */
    public function update(User $user, Media $media): bool
    {
        // Debug logging
        \Log::info('MediaPolicy::update check', [
            'user_id' => $user->id,
            'user_type' => $user->user_type->value,
            'user_current_team_id' => $user->currentTeam?->id,
            'media_id' => $media->id,
            'media_uploaded_by' => $media->uploaded_by,
            'media_company_id' => $media->company_id,
            'canAccessAllMedia' => $user->user_type->canAccessAllMedia(),
            'canManageCompanyMedia' => $user->user_type->canManageCompanyMedia(),
            'is_owner' => $media->uploaded_by === $user->id,
            'same_company' => $media->company_id === $user->currentTeam?->id,
            'can_upload_media' => $user->can_upload_media,
        ]);

        // Super admin can update any media
        if ($user->user_type->canAccessAllMedia()) {
            \Log::info('MediaPolicy::update - ALLOWED: Super admin');
            return true;
        }

        // Owner can update their own media
        if ($media->uploaded_by === $user->id) {
            \Log::info('MediaPolicy::update - ALLOWED: Owner');
            return true;
        }

        // Company admin can update media from their company
        if ($user->user_type->canManageCompanyMedia() &&
            $media->company_id === $user->currentTeam?->id) {
            \Log::info('MediaPolicy::update - ALLOWED: Company admin');
            return true;
        }

        // Individual agents with upload permission can update media from their company
        if ($user->can_upload_media &&
            $media->company_id === $user->currentTeam?->id) {
            \Log::info('MediaPolicy::update - ALLOWED: Individual agent with upload permission');
            return true;
        }

        \Log::info('MediaPolicy::update - DENIED: No matching conditions');
        return false;
    }

    /**
     * Determine whether the user can delete the media.
     */
    public function delete(User $user, Media $media): bool
    {
        // Super admin can delete any media
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        // Owner can delete their own media
        if ($media->uploaded_by === $user->id) {
            return true;
        }

        // Company admin can delete media from their company
        if ($user->user_type->canManageCompanyMedia() &&
            $media->company_id === $user->currentTeam?->id) {
            return true;
        }

        // Individual agents with upload permission can delete media from their company
        if ($user->can_upload_media &&
            $media->company_id === $user->currentTeam?->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the media.
     */
    public function restore(User $user, Media $media): bool
    {
        return $this->delete($user, $media);
    }

    /**
     * Determine whether the user can permanently delete the media.
     */
    public function forceDelete(User $user, Media $media): bool
    {
        return $user->user_type->canAccessAllMedia();
    }

    /**
     * Determine whether the user can manage media for a company.
     */
    public function manageCompanyMedia(User $user, ?int $companyId = null): bool
    {
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        if ($user->user_type->canManageCompanyMedia()) {
            return $companyId === null || $companyId === $user->currentTeam?->id;
        }

        return false;
    }

    /**
     * Determine whether the user can access company media.
     */
    public function accessCompanyMedia(User $user, ?int $companyId = null): bool
    {
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        if ($user->user_type->canManageCompanyMedia()) {
            return $companyId === null || $companyId === $user->currentTeam?->id;
        }

        if ($user->user_type === UserType::CHILD_AGENT && $user->can_access_company_media) {
            return $companyId === null || $companyId === $user->currentTeam?->id;
        }

        return false;
    }
}
