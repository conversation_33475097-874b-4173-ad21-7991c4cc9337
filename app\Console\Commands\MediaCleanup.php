<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Models\Media;
use App\Models\MediaFolder;

class MediaCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:cleanup
                            {--force : Force cleanup without confirmation}
                            {--files-only : Only delete files, keep database records}
                            {--db-only : Only delete database records, keep files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up all media files and database records';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Media Cleanup Tool');
        $this->info('===================');

        // Show current stats
        $this->showCurrentStats();

        // Confirmation
        if (!$this->option('force')) {
            if (!$this->confirm('⚠️  This will permanently delete ALL media files and database records. Are you sure?')) {
                $this->info('❌ Cleanup cancelled.');
                return 0;
            }

            if (!$this->confirm('🚨 FINAL WARNING: This action cannot be undone. Continue?')) {
                $this->info('❌ Cleanup cancelled.');
                return 0;
            }
        }

        $this->info('🚀 Starting cleanup...');

        try {
            if (!$this->option('db-only')) {
                $this->cleanupFiles();
            }

            if (!$this->option('files-only')) {
                $this->cleanupDatabase();
            }

            $this->info('✅ Media cleanup completed successfully!');
            $this->showCurrentStats();

        } catch (\Exception $e) {
            $this->error('❌ Error during cleanup: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Show current media statistics
     */
    private function showCurrentStats()
    {
        $mediaCount = Media::count();
        $folderCount = MediaFolder::count();

        $this->info("📊 Current Stats:");
        $this->info("   • Media files in DB: {$mediaCount}");
        $this->info("   • Media folders in DB: {$folderCount}");

        // Check storage directories
        $storageStats = $this->getStorageStats();
        $this->info("   • Storage directories: {$storageStats['directories']}");
        $this->info("   • Storage files: {$storageStats['files']}");
        $this->info("   • Storage size: {$storageStats['size']}");
        $this->newLine();
    }

    /**
     * Get storage statistics
     */
    private function getStorageStats()
    {
        $directories = 0;
        $files = 0;
        $totalSize = 0;

        try {
            // Check if media directory exists
            if (Storage::disk('public')->exists('media')) {
                $allFiles = Storage::disk('public')->allFiles('media');
                $allDirectories = Storage::disk('public')->allDirectories('media');

                $files = count($allFiles);
                $directories = count($allDirectories);

                foreach ($allFiles as $file) {
                    $totalSize += Storage::disk('public')->size($file);
                }
            }
        } catch (\Exception $e) {
            $this->warn("Could not read storage stats: " . $e->getMessage());
        }

        return [
            'directories' => $directories,
            'files' => $files,
            'size' => $this->formatBytes($totalSize)
        ];
    }

    /**
     * Clean up all media files from storage
     */
    private function cleanupFiles()
    {
        $this->info('🗂️  Cleaning up storage files...');

        $bar = $this->output->createProgressBar(3);
        $bar->start();

        try {
            // Delete entire media directory
            if (Storage::disk('public')->exists('media')) {
                Storage::disk('public')->deleteDirectory('media');
                $this->info('   ✅ Deleted media directory');
            } else {
                $this->info('   ℹ️  Media directory does not exist');
            }
            $bar->advance();

            // Delete any orphaned thumbnail directories
            if (Storage::disk('public')->exists('thumbnails')) {
                Storage::disk('public')->deleteDirectory('thumbnails');
                $this->info('   ✅ Deleted thumbnails directory');
            }
            $bar->advance();

            // Recreate empty media directory
            Storage::disk('public')->makeDirectory('media');
            $this->info('   ✅ Recreated empty media directory');
            $bar->advance();

        } catch (\Exception $e) {
            $this->error('   ❌ Error cleaning files: ' . $e->getMessage());
            throw $e;
        }

        $bar->finish();
        $this->newLine();
    }

    /**
     * Clean up all database records
     */
    private function cleanupDatabase()
    {
        $this->info('🗄️  Cleaning up database records...');

        $mediaCount = Media::count();
        $folderCount = MediaFolder::count();

        $bar = $this->output->createProgressBar($mediaCount + $folderCount + 2);
        $bar->start();

        try {
            // Delete media metadata first (if exists)
            if (DB::getSchemaBuilder()->hasTable('media_metadata')) {
                $metadataCount = DB::table('media_metadata')->count();
                DB::table('media_metadata')->delete();
                $this->info("   ✅ Deleted {$metadataCount} metadata records");
                $bar->advance();
            }

            // Delete all media records
            if ($mediaCount > 0) {
                // Use direct DB delete to avoid lazy loading issues
                DB::table('media')->delete();
                $this->info("   ✅ Deleted {$mediaCount} media records");

                // Advance progress bar for all media items
                for ($i = 0; $i < $mediaCount; $i++) {
                    $bar->advance();
                }
            } else {
                $this->info('   ℹ️  No media records to delete');
                $bar->advance();
            }

            // Delete all folder records
            if ($folderCount > 0) {
                // Use direct DB delete to avoid lazy loading issues
                DB::table('media_folders')->delete();
                $this->info("   ✅ Deleted {$folderCount} folder records");

                // Advance progress bar for all folder items
                for ($i = 0; $i < $folderCount; $i++) {
                    $bar->advance();
                }
            } else {
                $this->info('   ℹ️  No folder records to delete');
                $bar->advance();
            }

            // Reset auto-increment IDs
            DB::statement('ALTER TABLE media AUTO_INCREMENT = 1');
            DB::statement('ALTER TABLE media_folders AUTO_INCREMENT = 1');
            $this->info('   ✅ Reset auto-increment IDs');

        } catch (\Exception $e) {
            $this->error('   ❌ Error cleaning database: ' . $e->getMessage());
            throw $e;
        }

        $bar->finish();
        $this->newLine();
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
