<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Media;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class MediaHelper
{
    /**
     * Get media URL by ID.
     */
    public static function getUrl(?int $mediaId): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->url;
    }

    /**
     * Get media thumbnail URL by ID.
     */
    public static function getThumbnailUrl(?int $mediaId, ?string $size = null): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->getThumbnailUrl($size);
    }

    /**
     * Get media full URL by ID.
     */
    public static function getFullUrl(?int $mediaId): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->full_url;
    }

    /**
     * Get media model by ID with caching.
     */
    public static function getMedia(?int $mediaId): ?Media
    {
        if (!$mediaId) {
            return null;
        }

        return Cache::remember(
            "media.{$mediaId}",
            now()->addHours(24),
            fn() => Media::find($mediaId)
        );
    }

    /**
     * Get media with metadata by ID.
     */
    public static function getMediaWithMetadata(?int $mediaId): ?Media
    {
        if (!$mediaId) {
            return null;
        }

        return Cache::remember(
            "media.{$mediaId}.metadata",
            now()->addHours(24),
            fn() => Media::with('metadata')->find($mediaId)
        );
    }

    /**
     * Get media alt text by ID.
     */
    public static function getAltText(?int $mediaId): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->alt_text;
    }

    /**
     * Get media title by ID.
     */
    public static function getTitle(?int $mediaId): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->title;
    }

    /**
     * Check if media exists and is accessible by user.
     */
    public static function canAccess(?int $mediaId, $user): bool
    {
        if (!$mediaId || !$user) {
            return false;
        }

        $media = static::getMedia($mediaId);
        return $media?->canBeAccessedBy($user) ?? false;
    }

    /**
     * Get media dimensions (width x height).
     */
    public static function getDimensions(?int $mediaId): ?array
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMediaWithMetadata($mediaId);
        $metadata = $media?->metadata;

        if (!$metadata || !$metadata->width || !$metadata->height) {
            return null;
        }

        return [
            'width' => $metadata->width,
            'height' => $metadata->height,
        ];
    }

    /**
     * Get formatted file size.
     */
    public static function getFormattedSize(?int $mediaId): ?string
    {
        if (!$mediaId) {
            return null;
        }

        $media = static::getMedia($mediaId);
        return $media?->formatted_size;
    }

    /**
     * Check if media is an image.
     */
    public static function isImage(?int $mediaId): bool
    {
        if (!$mediaId) {
            return false;
        }

        $media = static::getMedia($mediaId);
        return $media?->isImage() ?? false;
    }

    /**
     * Check if media is a video.
     */
    public static function isVideo(?int $mediaId): bool
    {
        if (!$mediaId) {
            return false;
        }

        $media = static::getMedia($mediaId);
        return $media?->isVideo() ?? false;
    }

    /**
     * Clear media cache.
     */
    public static function clearCache(int $mediaId): void
    {
        Cache::forget("media.{$mediaId}");
        Cache::forget("media.{$mediaId}.metadata");
    }

    /**
     * Get media for profile image with fallback.
     */
    public static function getProfileImageUrl(?int $mediaId, ?string $fallbackPath = null): ?string
    {
        // Try to get media URL first
        $url = static::getUrl($mediaId);
        
        if ($url) {
            return $url;
        }

        // Fallback to old profile_photo_path if provided
        if ($fallbackPath) {
            return Storage::disk('public')->url($fallbackPath);
        }

        return null;
    }

    /**
     * Get thumbnail URL for profile image with fallback.
     */
    public static function getProfileThumbnailUrl(?int $mediaId, ?string $fallbackPath = null, ?string $size = null): ?string
    {
        // Try to get media thumbnail URL first
        $url = static::getThumbnailUrl($mediaId, $size);
        
        if ($url) {
            return $url;
        }

        // Fallback to original image if no thumbnail available
        return static::getProfileImageUrl($mediaId, $fallbackPath);
    }
}
