import React, { useState } from 'react';
import { useForm } from '@inertiajs/react';
import { format } from 'date-fns';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
} from '@/components/shadcn/ui/dialog';
import { Button } from '@/components/shadcn/ui/button';
import { Input } from '@/components/shadcn/ui/input';
import { Textarea } from '@/components/shadcn/ui/textarea';
import { Checkbox } from '@/components/shadcn/ui/checkbox';
import { Label } from '@/components/shadcn/ui/label';
import { Badge } from '@/components/shadcn/ui/badge';
import { Separator } from '@/components/shadcn/ui/separator';
import { ScrollArea } from '@/components/shadcn/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/shadcn/ui/alert';
import {
    FileImage,
    FileText,
    FileVideo,
    FileAudio,
    File,
    Download,
    Copy,
    Save,
    AlertCircle,
    Eye,
    Calendar,
    User,
    Building,
    HardDrive
} from 'lucide-react';

export default function MediaDetailsModal({
    isOpen,
    onClose,
    media,
    onUpdate,
    userPermissions,
    onMove,
    onDelete
}) {
    const [isEditing, setIsEditing] = useState(false);
    const [copied, setCopied] = useState(false);

    const { data, setData, put, processing, errors, reset } = useForm({
        title: media?.title || '',
        alt_text: media?.alt_text || '',
        description: media?.description || '',
    });

    // Early return if no media (after hooks)
    if (!media) {
        return null;
    }

    // Get file icon
    const getFileIcon = () => {
        const iconClass = "h-8 w-8 text-muted-foreground";

        if (media?.file_type === 'image') {
            return <FileImage className={iconClass} />;
        } else if (media?.file_type === 'video') {
            return <FileVideo className={iconClass} />;
        } else if (media?.file_type === 'audio') {
            return <FileAudio className={iconClass} />;
        } else if (media?.file_type === 'document') {
            return <FileText className={iconClass} />;
        }

        return <File className={iconClass} />;
    };

    // Handle form submission
    const handleSubmit = (e) => {
        e.preventDefault();

        put(route('admin.media.update', media.id), {
            onSuccess: (page) => {
                setIsEditing(false);
                onUpdate(page.props.media);
            },
            onError: (errors) => {
                console.error('Failed to update media:', errors);
            }
        });
    };

    // Handle copy URL
    const handleCopyUrl = async () => {
        try {
            await navigator.clipboard.writeText(media.full_url);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy URL:', err);
        }
    };

    // Handle download
    const handleDownload = () => {
        const link = document.createElement("a");
        link.href = media.url;
        link.download = media.name || "download";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Handle close
    const handleClose = () => {
        if (!processing) {
            setIsEditing(false);
            reset();
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        {getFileIcon()}
                        {media.title || media.original_filename}
                    </DialogTitle>
                    <DialogDescription>
                        Media file details and metadata
                    </DialogDescription>
                </DialogHeader>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-hidden">
                    {/* Preview Section */}
                    <div className="space-y-4">
                        <div className="aspect-video bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                            {media.file_type === 'image' ? (
                                <img
                                    src={media.url}
                                    alt={media.alt_text || media.original_filename}
                                    className="max-w-full max-h-full object-contain"
                                />
                            ) : media.file_type === 'video' ? (
                                <video
                                    src={media.url}
                                    controls
                                    className="max-w-full max-h-full"
                                >
                                    Your browser does not support the video tag.
                                </video>
                            ) : media.file_type === 'audio' ? (
                                <audio
                                    src={media.url}
                                    controls
                                    className="w-full"
                                >
                                    Your browser does not support the audio tag.
                                </audio>
                            ) : (
                                <div className="text-center">
                                    {getFileIcon()}
                                    <p className="mt-2 text-sm text-muted-foreground">
                                        Preview not available
                                    </p>
                                </div>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 flex-wrap">
                            <Button variant="outline" onClick={handleDownload}>
                                <Download className="h-4 w-4 mr-2" />
                                Download
                            </Button>
                            <Button
                                variant="outline"
                                onClick={handleCopyUrl}
                                className={copied ? "text-green-600" : ""}
                            >
                                <Copy className="h-4 w-4 mr-2" />
                                {copied ? 'Copied!' : 'Copy URL'}
                            </Button>
                            <Button variant="outline" onClick={() => window.open(media.url, '_blank')}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Full Size
                            </Button>
                        </div>
                    </div>

                    {/* Details Section */}
                    <ScrollArea className="h-[60vh]">
                        <div className="space-y-6 pr-4">
                            {/* Editable Fields */}
                            {isEditing ? (
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="title">Title</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            placeholder="Enter title"
                                            disabled={processing}
                                        />
                                        {errors.title && (
                                            <Alert className="border-red-200 bg-red-50">
                                                <AlertCircle className="h-4 w-4 text-red-600" />
                                                <AlertDescription className="text-red-600">
                                                    {errors.title}
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="alt_text">Alt Text</Label>
                                        <Input
                                            id="alt_text"
                                            value={data.alt_text}
                                            onChange={(e) => setData('alt_text', e.target.value)}
                                            placeholder="Enter alt text for accessibility"
                                            disabled={processing}
                                        />
                                        {errors.alt_text && (
                                            <Alert className="border-red-200 bg-red-50">
                                                <AlertCircle className="h-4 w-4 text-red-600" />
                                                <AlertDescription className="text-red-600">
                                                    {errors.alt_text}
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Enter description"
                                            disabled={processing}
                                            rows={3}
                                        />
                                        {errors.description && (
                                            <Alert className="border-red-200 bg-red-50">
                                                <AlertCircle className="h-4 w-4 text-red-600" />
                                                <AlertDescription className="text-red-600">
                                                    {errors.description}
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                    </div>



                                    <div className="flex gap-2">
                                        <Button type="submit" disabled={processing}>
                                            <Save className="h-4 w-4 mr-2" />
                                            {processing ? 'Saving...' : 'Save Changes'}
                                        </Button>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                setIsEditing(false);
                                                reset();
                                            }}
                                            disabled={processing}
                                        >
                                            Cancel
                                        </Button>
                                    </div>
                                </form>
                            ) : (
                                <div className="space-y-4">
                                    {/* Basic Info */}
                                    <div>
                                        <h3 className="font-medium mb-2">File Information</h3>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Filename:</span>
                                                <span className="font-mono">{media.original_filename}</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Type:</span>
                                                <Badge variant="outline">
                                                    {media.extension?.toUpperCase() || 'FILE'}
                                                </Badge>
                                            </div>
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">Size:</span>
                                                <span>{media.formatted_size}</span>
                                            </div>

                                        </div>
                                    </div>

                                    <Separator />

                                    {/* Metadata */}
                                    <div>
                                        <h3 className="font-medium mb-2">Details</h3>
                                        <div className="space-y-2 text-sm">
                                            {media.title && (
                                                <div>
                                                    <span className="text-muted-foreground">Title:</span>
                                                    <p className="mt-1">{media.title}</p>
                                                </div>
                                            )}
                                            {media.alt_text && (
                                                <div>
                                                    <span className="text-muted-foreground">Alt Text:</span>
                                                    <p className="mt-1">{media.alt_text}</p>
                                                </div>
                                            )}
                                            {media.description && (
                                                <div>
                                                    <span className="text-muted-foreground">Description:</span>
                                                    <p className="mt-1">{media.description}</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <Separator />

                                    {/* Upload Info */}
                                    <div>
                                        <h3 className="font-medium mb-2">Upload Information</h3>
                                        <div className="space-y-2 text-sm">
                                            <div className="flex items-center gap-2">
                                                <User className="h-4 w-4 text-muted-foreground" />
                                                <span>Uploaded by {media.uploader?.name || 'Unknown'}</span>
                                            </div>
                                            {media.company && (
                                                <div className="flex items-center gap-2">
                                                    <Building className="h-4 w-4 text-muted-foreground" />
                                                    <span>{media.company.name}</span>
                                                </div>
                                            )}
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                                <span>{format(new Date(media.created_at), 'PPP')}</span>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <HardDrive className="h-4 w-4 text-muted-foreground" />
                                                <span className="font-mono text-xs">{media.uuid}</span>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Technical Metadata */}
                                    {media.metadata && (
                                        <>
                                            <Separator />
                                            <div>
                                                <h3 className="font-medium mb-2">Technical Details</h3>
                                                <div className="space-y-2 text-sm">
                                                    {media.metadata.width && media.metadata.height && (
                                                        <div className="flex justify-between">
                                                            <span className="text-muted-foreground">Dimensions:</span>
                                                            <span>{media.metadata.width} × {media.metadata.height}</span>
                                                        </div>
                                                    )}
                                                    {media.metadata.duration && (
                                                        <div className="flex justify-between">
                                                            <span className="text-muted-foreground">Duration:</span>
                                                            <span>{media.metadata.formatted_duration}</span>
                                                        </div>
                                                    )}
                                                    {media.metadata.camera_make && (
                                                        <div className="flex justify-between">
                                                            <span className="text-muted-foreground">Camera:</span>
                                                            <span>{media.metadata.camera_make} {media.metadata.camera_model}</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {userPermissions.canUpload && (
                                        <div className="flex gap-2">
                                            <Button
                                                onClick={() => setIsEditing(true)}
                                                data-edit-button
                                            >
                                                Edit Details
                                            </Button>

                                            {onMove && (
                                                <Button
                                                    variant="outline"
                                                    onClick={() => {
                                                        onMove(media);
                                                        onClose();
                                                    }}
                                                >
                                                    Move
                                                </Button>
                                            )}

                                            {onDelete && (
                                                <Button
                                                    variant="destructive"
                                                    onClick={() => {
                                                        onDelete(media);
                                                        onClose();
                                                    }}
                                                >
                                                    Delete
                                                </Button>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </ScrollArea>
                </div>
            </DialogContent>
        </Dialog>
    );
}
