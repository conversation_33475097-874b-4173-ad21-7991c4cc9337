import * as React from "react"
import { Search, X } from "lucide-react"

import { cn } from "@/Components/lib/utils"
import { Input } from "@/Components/shadcn/ui/input"
import { Button } from "@/Components/shadcn/ui/button"

const SearchBar = React.forwardRef(({ 
  className, 
  value, 
  onChange, 
  onClear,
  placeholder = "Search...",
  showClearButton = true,
  ...props 
}, ref) => {
  const handleClear = () => {
    if (onClear) {
      onClear()
    } else if (onChange) {
      onChange({ target: { value: '' } })
    }
  }

  return (
    <div className={cn("relative", className)}>
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        ref={ref}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="pl-9 pr-9"
        {...props}
      />
      {showClearButton && value && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-transparent"
          onClick={handleClear}
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Clear search</span>
        </Button>
      )}
    </div>
  )
})
SearchBar.displayName = "SearchBar"

export { SearchBar }
