<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Models\Plan;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;

class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        return Inertia::render('Admin/Plans/Index');
    }

    /**
     * API endpoint for DataTable
     */
    public function apiIndex(Request $request)
    {
        $search = $request->get('search');
        $perPage = $request->get('per_page', 10);
        $sortColumn = $request->get('sort', 'sort_order');
        $sortDirection = $request->get('direction', 'asc');

        // Handle filters
        $filterName = $request->get('filter_name');
        $filterDescription = $request->get('filter_description');
        $filterBillingPeriod = $request->get('filter_billing_period');
        $filterIsActive = $request->get('filter_is_active');
        $filterCreatedFrom = $request->get('filter_created_at_from');
        $filterCreatedTo = $request->get('filter_created_at_to');

        $query = Plan::query();

        // Apply search
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('stripe_price_id', 'like', "%{$search}%");
            });
        }

        // Apply filters
        if ($filterName) {
            $query->where('name', 'like', "%{$filterName}%");
        }

        if ($filterDescription) {
            $query->where('description', 'like', "%{$filterDescription}%");
        }

        if ($filterBillingPeriod) {
            $query->where('billing_period', $filterBillingPeriod);
        }

        if ($filterIsActive !== null && $filterIsActive !== '') {
            $query->where('is_active', $filterIsActive === 'true' || $filterIsActive === '1');
        }

        if ($filterCreatedFrom) {
            $query->whereDate('created_at', '>=', $filterCreatedFrom);
        }

        if ($filterCreatedTo) {
            $query->whereDate('created_at', '<=', $filterCreatedTo);
        }

        // Apply sorting
        $allowedSortColumns = ['name', 'price', 'billing_period', 'is_active', 'is_popular', 'sort_order', 'created_at'];
        if (in_array($sortColumn, $allowedSortColumns)) {
            $query->orderBy($sortColumn, $sortDirection);
        } else {
            $query->ordered();
        }

        $plans = $query->paginate($perPage);

        // Add formatted attributes
        $plans->getCollection()->transform(function ($plan) {
            $plan->formatted_price = $plan->formatted_price;
            $plan->features_count = is_array($plan->features) ? count($plan->features) : 0;
            return $plan;
        });

        return response()->json($plans);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        return Inertia::render('Admin/Plans/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'billing_period' => ['required', 'in:monthly,yearly'],
            'stripe_price_id' => ['nullable', 'string', 'max:255'],
            'features' => ['nullable', 'array'],
            'features.*' => ['string'],
            'is_active' => ['boolean'],
            'is_popular' => ['boolean'],
            'max_agents' => ['nullable', 'integer', 'min:1'],
            'max_listings' => ['nullable', 'integer', 'min:1'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        Plan::create($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Plan $plan): Response
    {
        return Inertia::render('Admin/Plans/Show', [
            'plan' => $plan,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plan $plan): Response
    {
        return Inertia::render('Admin/Plans/Edit', [
            'plan' => $plan,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plan $plan): RedirectResponse
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'billing_period' => ['required', 'in:monthly,yearly'],
            'stripe_price_id' => ['nullable', 'string', 'max:255'],
            'features' => ['nullable', 'array'],
            'features.*' => ['string'],
            'is_active' => ['boolean'],
            'is_popular' => ['boolean'],
            'max_agents' => ['nullable', 'integer', 'min:1'],
            'max_listings' => ['nullable', 'integer', 'min:1'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $plan->update($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan): RedirectResponse
    {
        $plan->delete();

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan deleted successfully.');
    }
}
