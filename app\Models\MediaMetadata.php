<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MediaMetadata extends Model
{
    protected $fillable = [
        'media_id',
        'width',
        'height',
        'duration',
        'bitrate',
        'framerate',
        'channels',
        'exif_data',
        'color_profile',
        'camera_make',
        'camera_model',
        'focal_length',
        'aperture',
        'iso',
        'shutter_speed',
        'gps_latitude',
        'gps_longitude',
        'keywords',
        'additional_metadata',
    ];
    
    protected $casts = [
        'media_id' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'duration' => 'integer',
        'bitrate' => 'integer',
        'framerate' => 'float',
        'channels' => 'integer',
        'focal_length' => 'float',
        'aperture' => 'float',
        'iso' => 'integer',
        'shutter_speed' => 'float',
        'gps_latitude' => 'float',
        'gps_longitude' => 'float',
        'exif_data' => 'array',
        'keywords' => 'array',
        'additional_metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * Get the media that owns this metadata.
     */
    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class);
    }
    
    /**
     * Get aspect ratio.
     */
    public function getAspectRatio(): ?float
    {
        if ($this->width && $this->height) {
            return round($this->width / $this->height, 2);
        }
        
        return null;
    }
    
    /**
     * Get resolution string.
     */
    public function getResolution(): ?string
    {
        if ($this->width && $this->height) {
            return $this->width . 'x' . $this->height;
        }
        
        return null;
    }
    
    /**
     * Get formatted duration.
     */
    public function getFormattedDuration(): ?string
    {
        if (!$this->duration) {
            return null;
        }
        
        $hours = floor($this->duration / 3600);
        $minutes = floor(($this->duration % 3600) / 60);
        $seconds = $this->duration % 60;
        
        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
    
    /**
     * Check if GPS coordinates are available.
     */
    public function hasGpsCoordinates(): bool
    {
        return $this->gps_latitude !== null && $this->gps_longitude !== null;
    }
    
    /**
     * Get GPS coordinates as array.
     */
    public function getGpsCoordinates(): ?array
    {
        if ($this->hasGpsCoordinates()) {
            return [
                'latitude' => $this->gps_latitude,
                'longitude' => $this->gps_longitude,
            ];
        }
        
        return null;
    }
    
    /**
     * Get camera information.
     */
    public function getCameraInfo(): ?array
    {
        if ($this->camera_make || $this->camera_model) {
            return [
                'make' => $this->camera_make,
                'model' => $this->camera_model,
                'focal_length' => $this->focal_length,
                'aperture' => $this->aperture,
                'iso' => $this->iso,
                'shutter_speed' => $this->shutter_speed,
            ];
        }
        
        return null;
    }
}
