<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Services\ThumbnailService;

class Media extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'filename',
        'original_filename',
        'mime_type',
        'extension',
        'size',
        'disk',
        'path',
        'uploaded_by',
        'company_id',
        'folder_id',
        'alt_text',
        'title',
        'description',
    ];

    protected $appends = [
        'url',
        'thumbnail_url',
        'full_url',
        'formatted_size',
        'file_type',
    ];

    protected $casts = [
        'id' => 'integer',
        'uploaded_by' => 'integer',
        'company_id' => 'integer',
        'size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($media) {
            $media->uuid = Str::uuid();
        });
    }

    /**
     * Get the user who uploaded this media.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the company this media belongs to.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'company_id');
    }

    /**
     * Get the folder this media belongs to.
     */
    public function folder(): BelongsTo
    {
        return $this->belongsTo(MediaFolder::class, 'folder_id');
    }

    /**
     * Get the media metadata.
     */
    public function metadata(): HasOne
    {
        return $this->hasOne(MediaMetadata::class);
    }

    /**
     * Get the media URL.
     */
    public function url(): string
    {
        return Storage::disk($this->disk)->url($this->path);
    }

    /**
     * Get the URL attribute.
     */
    public function getUrlAttribute(): string
    {
        return $this->url();
    }

    /**
     * Get the full URL attribute.
     */
    public function getFullUrlAttribute(): string
    {
        return Storage::disk($this->disk)->url($this->path);
    }

    /**
     * Check if the media is an image.
     */
    public function isImage(): bool
    {
        return in_array($this->extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp']);
    }

    /**
     * Check if the media is a video.
     */
    public function isVideo(): bool
    {
        return in_array($this->extension, ['mp4', 'webm', 'ogg', 'mov', 'avi', 'wmv', 'flv']);
    }

    /**
     * Check if the media is audio.
     */
    public function isAudio(): bool
    {
        return in_array($this->extension, ['mp3', 'wav', 'ogg', 'aac', 'flac']);
    }

    /**
     * Check if the media is a document.
     */
    public function isDocument(): bool
    {
        return in_array($this->extension, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt']);
    }

    /**
     * Get the file type category.
     */
    public function getFileTypeAttribute(): string
    {
        if ($this->isImage()) return 'image';
        if ($this->isVideo()) return 'video';
        if ($this->isAudio()) return 'audio';
        if ($this->isDocument()) return 'document';
        return 'other';
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedSize(): string
    {
        $bytes = $this->size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        }

        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        }

        if ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        }

        return $bytes . ' bytes';
    }

    /**
     * Get the formatted size attribute.
     */
    public function getFormattedSizeAttribute(): string
    {
        return $this->getFormattedSize();
    }

    /**
     * Get thumbnail path.
     */
    public function getThumbnailPath(): string
    {
        $pathInfo = pathinfo($this->path);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['basename'];

        return $directory . '/thumbnails/' . $filename;
    }

    /**
     * Get thumbnail URL for a specific size.
     */
    public function getThumbnailUrl(?string $size = null): ?string
    {
        if (!$this->isImage()) {
            return null;
        }

        $thumbnailService = app(ThumbnailService::class);
        return $thumbnailService->getThumbnailUrl($this, $size);
    }

    /**
     * Get the thumbnail URL attribute (default size).
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        return $this->getThumbnailUrl();
    }

    /**
     * Generate thumbnails for this media file.
     */
    public function generateThumbnails(): array
    {
        $thumbnailService = app(ThumbnailService::class);
        return $thumbnailService->generateThumbnails($this);
    }

    /**
     * Delete all thumbnails for this media file.
     */
    public function deleteThumbnails(): void
    {
        $thumbnailService = app(ThumbnailService::class);
        $thumbnailService->deleteThumbnails($this);
    }

    /**
     * Get storage path for the media file.
     */
    public function getStoragePath(): string
    {
        return $this->path;
    }

    /**
     * Delete media file and cleanup.
     */
    public function delete(): bool
    {
        try {
            // Delete all thumbnails first (if it's an image)
            if ($this->isImage()) {
                $this->deleteThumbnails();
            }

            // Delete the actual file
            if (Storage::disk($this->disk)->exists($this->path)) {
                Storage::disk($this->disk)->delete($this->path);
            }

            // Delete metadata (load it first to avoid lazy loading issues)
            $metadata = $this->metadata()->first();
            if ($metadata) {
                $metadata->delete();
            }

            return parent::delete();
        } catch (\Exception $e) {
            \Log::error('Failed to delete media file', [
                'media_id' => $this->id,
                'path' => $this->path,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if user can access this media.
     */
    public function canBeAccessedBy(User $user): bool
    {
        // Super admin can access everything
        if ($user->user_type->canAccessAllMedia()) {
            return true;
        }

        // Owner can always access their own media
        if ($this->uploaded_by === $user->id) {
            return true;
        }

        // Company admin can access media from their company and child agents
        if ($user->user_type->canManageCompanyMedia() && $this->company_id === $user->currentTeam?->id) {
            return true;
        }

        // Child agents can access company media if they belong to the same company
        if ($user->user_type === \App\Enums\UserType::CHILD_AGENT &&
            $this->company_id === $user->currentTeam?->id) {
            return true;
        }

        return false;
    }
}
