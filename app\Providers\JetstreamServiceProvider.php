<?php

declare(strict_types=1);

namespace App\Providers;

use Illuminate\Http\Request;
use Laravel\Jetstream\Jetstream;
use App\Actions\Jetstream\CreateTeam;
use App\Actions\Jetstream\DeleteTeam;
use App\Actions\Jetstream\DeleteUser;
use Illuminate\Support\ServiceProvider;
use App\Actions\Jetstream\AddTeamMember;
use App\Actions\Jetstream\UpdateTeamName;
use App\Actions\Jetstream\InviteTeamMember;
use App\Actions\Jetstream\RemoveTeamMember;
use App\Actions\User\ActiveOauthProviderAction;

final class JetstreamServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->configurePermissions();

        Jetstream::createTeamsUsing(CreateTeam::class);
        Jetstream::updateTeamNamesUsing(UpdateTeamName::class);
        Jetstream::addTeamMembersUsing(AddTeamMember::class);
        Jetstream::inviteTeamMembersUsing(InviteTeamMember::class);
        Jetstream::removeTeamMembersUsing(RemoveTeamMember::class);
        Jetstream::deleteTeamsUsing(DeleteTeam::class);
        Jetstream::deleteUsersUsing(DeleteUser::class);

        Jetstream::inertia()->whenRendering(
            'Profile/Show',
            fn (Request $request, array $data): array => array_merge($data, [
                'availableOauthProviders' => (new ActiveOauthProviderAction)->handle(),
                'activeOauthProviders' => $request->user()?->oauthConnections->pluck('provider'),
                'user' => $request->user()?->load('profileImage'),
            ])
        );
    }

    /**
     * Configure the roles and permissions that are available within the application.
     */
    private function configurePermissions(): void
    {
        Jetstream::defaultApiTokenPermissions(['read']);

        // Real Estate specific roles
        Jetstream::role('agency_admin', 'Agency Administrator', [
            'create',
            'read',
            'update',
            'delete',
            'manage_agents',
            'manage_listings',
            'manage_leads',
            'view_reports',
        ])->description('Agency administrators can manage all aspects of the agency including agents and listings.');

        Jetstream::role('broker', 'Broker', [
            'create',
            'read',
            'update',
            'delete',
            'manage_agents',
            'manage_listings',
            'manage_leads',
            'view_reports',
            'approve_transactions',
        ])->description('Licensed brokers can manage agents, listings, and approve transactions.');

        Jetstream::role('senior_agent', 'Senior Agent', [
            'create',
            'read',
            'update',
            'manage_listings',
            'manage_leads',
            'view_reports',
        ])->description('Senior agents can manage their own listings and leads, and view team reports.');

        Jetstream::role('agent', 'Agent', [
            'create',
            'read',
            'update',
            'manage_listings',
            'manage_leads',
        ])->description('Agents can manage their own listings and leads.');

        Jetstream::role('assistant', 'Assistant', [
            'read',
            'update',
            'manage_leads',
        ])->description('Assistants can help with lead management and basic updates.');

        // Keep original roles for backward compatibility
        Jetstream::role('admin', 'Administrator', [
            'create',
            'read',
            'update',
            'delete',
        ])->description('Administrator users can perform any action.');

        Jetstream::role('editor', 'Editor', [
            'read',
            'create',
            'update',
        ])->description('Editor users have the ability to read, create, and update resources.');

        Jetstream::role('viewer', 'Viewer', [
            'read',
        ])->description('Viewer users have the ability to read resources.');
    }
}
