import React, { useState } from 'react';
import { Card, CardContent } from '@/Components/shadcn/ui/card';
import { Button } from '@/Components/shadcn/ui/button';
import { Badge } from '@/Components/shadcn/ui/badge';
import { Label } from '@/Components/shadcn/ui/label';
import { Upload, X, File, FileText, FileImage, FileVideo, FileAudio, Archive, Code } from 'lucide-react';
import MediaPickerModal from './MediaPickerModal';

// File type icon mapping
const getFileIcon = (media) => {
    if (!media || !media.file_type) return <File className="h-8 w-8 text-muted-foreground" />;
    
    const fileType = media.file_type.toLowerCase();
    const mimeType = media.mime_type?.toLowerCase() || '';
    
    // Image files
    if (fileType === 'image' || mimeType.startsWith('image/')) {
        return <FileImage className="h-8 w-8 text-blue-500" />;
    }
    
    // Video files
    if (fileType === 'video' || mimeType.startsWith('video/')) {
        return <FileVideo className="h-8 w-8 text-purple-500" />;
    }
    
    // Audio files
    if (fileType === 'audio' || mimeType.startsWith('audio/')) {
        return <FileAudio className="h-8 w-8 text-green-500" />;
    }
    
    // Archive files
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('tar')) {
        return <Archive className="h-8 w-8 text-orange-500" />;
    }
    
    // Code files
    if (mimeType.includes('javascript') || mimeType.includes('json') || mimeType.includes('xml') || 
        media.original_filename?.match(/\.(js|jsx|ts|tsx|php|py|css|html|xml|json)$/i)) {
        return <Code className="h-8 w-8 text-indigo-500" />;
    }
    
    // Text files
    if (fileType === 'document' || mimeType.startsWith('text/') || mimeType.includes('document')) {
        return <FileText className="h-8 w-8 text-gray-500" />;
    }
    
    // Default file icon
    return <File className="h-8 w-8 text-muted-foreground" />;
};

export default function MediaFileSelector({
    label,
    value,
    onChange,
    selectedMedia = null,
    accept = 'all',
    required = false,
    placeholder = 'Click to select file',
    description = null,
    error = null,
    className = '',
    disabled = false
}) {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentMedia, setCurrentMedia] = useState(selectedMedia);

    // Update currentMedia when selectedMedia prop changes
    React.useEffect(() => {
        setCurrentMedia(selectedMedia);
    }, [selectedMedia]);

    const handleMediaSelect = (media) => {
        console.log('MediaFileSelector - handleMediaSelect called with:', media);
        setCurrentMedia(media); // Immediate preview update
        onChange(media.id, media);
        setIsModalOpen(false);
    };

    const handleClear = () => {
        console.log('MediaFileSelector - handleClear called');
        setCurrentMedia(null); // Clear preview immediately
        onChange(null, null);
    };

    return (
        <div className={`space-y-2 ${className}`}>
            {label && (
                <Label className="text-sm font-medium">
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </Label>
            )}

            {description && (
                <p className="text-sm text-muted-foreground">{description}</p>
            )}

            {/* Selected Media Display - Rectangular Card Design */}
            {currentMedia ? (
                <Card className="border-2 border-primary/20 overflow-hidden">
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                            {/* File Icon */}
                            <div className="flex-shrink-0">
                                <div className="w-16 h-16 rounded-lg bg-muted flex items-center justify-center">
                                    {getFileIcon(currentMedia)}
                                </div>
                            </div>

                            {/* File Info */}
                            <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-foreground truncate">
                                    {currentMedia.title || currentMedia.original_filename}
                                </p>
                                <div className="flex items-center space-x-2 mt-1">
                                    <Badge variant="secondary" className="text-xs">
                                        {currentMedia.file_type || 'unknown'}
                                    </Badge>
                                    {currentMedia.formatted_size && (
                                        <span className="text-xs text-muted-foreground">
                                            {currentMedia.formatted_size}
                                        </span>
                                    )}
                                </div>
                                {currentMedia.alt_text && (
                                    <p className="text-xs text-muted-foreground mt-1 truncate">
                                        {currentMedia.alt_text}
                                    </p>
                                )}
                                {/* File path/URL for reference */}
                                {currentMedia.url && (
                                    <p className="text-xs text-muted-foreground mt-1 truncate font-mono">
                                        {currentMedia.url}
                                    </p>
                                )}
                            </div>

                            {/* Actions */}
                            <div className="flex items-center space-x-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => setIsModalOpen(true)}
                                    disabled={disabled}
                                >
                                    Change
                                </Button>
                                <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={handleClear}
                                    disabled={disabled}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            ) : (
                /* Empty State - Rectangular Card */
                <Card 
                    className={`border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => !disabled && setIsModalOpen(true)}
                >
                    <CardContent className="p-6 text-center">
                        <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-sm font-medium text-foreground mb-2">
                            {placeholder}
                        </p>
                        <p className="text-xs text-muted-foreground">
                            Browse from media library or upload new files
                        </p>
                    </CardContent>
                </Card>
            )}

            {error && (
                <p className="text-sm text-red-500">{error}</p>
            )}

            {/* Media Picker Modal */}
            <MediaPickerModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSelect={handleMediaSelect}
                accept={accept}
                title="Select File"
            />
        </div>
    );
}
