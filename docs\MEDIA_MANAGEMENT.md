# Media Management System

## Overview

The Media Management System provides a comprehensive solution for handling file uploads, storage, and access control in a multi-tenant Laravel application with role-based permissions.

## Database Structure

### Tables Created

1. **`media`** - Stores media file information
2. **`media_metadata`** - Stores detailed metadata for media files
3. **`media_collections`** - Organizes media into collections
4. **`media_collection_items`** - Pivot table for media-collection relationships
5. **`users`** - Updated with new user types and media permissions

### User Types & Hierarchy

```
Platform Administrator (platform_administrator)
├── Brokerage Admin (brokerage_admin)
│   ├── Independent Agent (independent_agent)
│   └── Company Agent (company_agent)
└── Independent Agent (independent_agent)
```

## User Types & Permissions

### Platform Administrator (`platform_administrator`)
- **Full Access**: Can view, edit, delete all media files and collections
- **System Management**: Can manage all users and companies
- **No Restrictions**: Bypasses all access control checks

### Brokerage Admin (`brokerage_admin`)
- **Company Media**: Can manage all media within their company
- **Team Management**: Can add/remove company agents
- **Hierarchical Access**: Can access media from company agents
- **Collection Management**: Can create and manage company collections

### Individual Agent (`individual_agent`)
- **Personal Media**: Can only access their own uploaded media
- **Public Media**: Can view publicly accessible media
- **Independent**: Not part of any company hierarchy

### Child Agent (`child_agent`)
- **Personal Media**: Can access their own uploaded media
- **Company Media**: Can access company media (if `can_access_company_media` is true)
- **Restricted**: Cannot manage other users' media
- **Team Member**: Belongs to a parent company

## Models & Relationships

### Media Model
```php
// Relationships
$media->uploader()      // User who uploaded
$media->company()       // Company it belongs to
$media->metadata()      // Detailed metadata

// Access Control
$media->canBeAccessedBy($user)  // Check if user can access

// File Type Detection
$media->isImage()
$media->isVideo()
$media->isAudio()
$media->isDocument()
```

### MediaMetadata Model
```php
// Image/Video Properties
$metadata->width, $metadata->height
$metadata->getAspectRatio()
$metadata->getResolution()

// Video/Audio Properties
$metadata->duration
$metadata->getFormattedDuration()

// Camera/EXIF Data
$metadata->camera_make, $metadata->camera_model
$metadata->getCameraInfo()

// GPS Coordinates
$metadata->hasGpsCoordinates()
$metadata->getGpsCoordinates()
```

### MediaCollection Model
```php
// Relationships
$collection->creator()  // User who created
$collection->company()  // Company it belongs to
$collection->media()    // Media files in collection

// Access Control
$collection->canBeAccessedBy($user)
```

### User Model Extensions
```php
// New User Types
$user->isSuperAdmin()
$user->isChildAgent()

// Media Relationships
$user->uploadedMedia()      // Media uploaded by user
$user->mediaCollections()   // Collections created by user
$user->getAccessibleMedia() // Media user can access

// Company Relationships
$user->parentCompany()      // For child agents
```

### Team Model Extensions
```php
// Media Relationships
$team->media()              // Company media
$team->mediaCollections()   // Company collections
$team->childAgents()        // Child agents in company
```

## Access Control Logic

### Media Access Rules

1. **Super Admin**: Access to all media
2. **Public Media**: Accessible by all authenticated users
3. **Owner Access**: Users can always access their own media
4. **Company Access**: Company admins can access company media
5. **Child Agent Access**: Can access company media if permission granted

### Collection Access Rules

1. **Super Admin**: Access to all collections
2. **Public Collections**: Accessible by all authenticated users
3. **Creator Access**: Users can always access their own collections
4. **Company Access**: Company admins can access company collections
5. **Child Agent Access**: Can access company collections if in same company

## Policies

### MediaPolicy
- `viewAny()` - Can view media list
- `view()` - Can view specific media
- `create()` - Can upload media
- `update()` - Can edit media
- `delete()` - Can delete media
- `manageCompanyMedia()` - Can manage company media
- `accessCompanyMedia()` - Can access company media

### MediaCollectionPolicy
- `viewAny()` - Can view collection list
- `view()` - Can view specific collection
- `create()` - Can create collections
- `update()` - Can edit collections
- `delete()` - Can delete collections
- `addMedia()` - Can add media to collection
- `removeMedia()` - Can remove media from collection

## Database Migrations

### Migration Files Created
1. `2025_09_20_184539_create_media_table.php`
2. `2025_09_20_184614_create_media_metadata_table.php`
3. `2025_09_20_184639_update_users_table_for_media_management.php`
4. `2025_09_20_184728_create_media_collections_table.php`
5. `2025_09_20_184751_create_media_collection_items_table.php`

### Key Database Features
- **UUID Support**: All media files have unique UUIDs
- **Soft Deletes**: Media and collections support soft deletion
- **Foreign Key Constraints**: Proper relationships with cascade/null on delete
- **Indexes**: Optimized for common queries
- **JSON Fields**: Flexible metadata storage

## Sample Data

### Test Users Created
- **Super Admin**: <EMAIL>
- **Company Admin**: <EMAIL> (ABC Real Estate)
- **Individual Agent**: <EMAIL>
- **Child Agent**: <EMAIL> (belongs to ABC Real Estate)

All users have password: `password`

### Sample Collections
- **Public Assets**: Publicly accessible media
- **Company Assets**: ABC Real Estate company media

## Next Steps

### Backend Implementation
1. Create MediaController for file upload/management
2. Create MediaCollectionController for collection management
3. Implement file upload validation and processing
4. Add thumbnail generation for images
5. Create API endpoints for frontend integration

### Frontend Implementation
1. Create React components for media library
2. Implement file upload with drag & drop
3. Build media selection modals
4. Create collection management interface
5. Add media preview and metadata display

### Additional Features
1. File versioning system
2. Media tagging and search
3. Bulk operations (upload, delete, move)
4. Media usage tracking
5. Storage quota management
6. CDN integration for performance
