<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\UserType;

class CompanyScope
{
    /**
     * Handle an incoming request to ensure company-scoped access.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            abort(401, 'Unauthenticated');
        }

        // Platform Administrator can access everything
        if ($user->user_type === UserType::PLATFORM_ADMINISTRATOR) {
            return $next($request);
        }

        // For company-scoped users, ensure they have a current team
        if (in_array($user->user_type, [
            UserType::BROKERAGE_ADMIN,
            UserType::COMPANY_AGENT
        ])) {
            if (!$user->currentTeam) {
                abort(403, 'No company assigned');
            }

            // Add company scope to request for downstream usage
            $request->attributes->set('company_id', $user->currentTeam->id);
        }

        return $next($request);
    }
}
