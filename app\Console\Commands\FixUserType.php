<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Enums\UserType;

class FixUserType extends Command
{
    protected $signature = 'user:fix-type {user_id} {type}';
    protected $description = 'Fix user type for a specific user';

    public function handle()
    {
        $userId = $this->argument('user_id');
        $type = $this->argument('type');
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("User {$userId} not found");
            return 1;
        }
        
        $this->info("Current user type: " . $user->user_type->value);
        $this->info("canAccessAllMedia: " . ($user->user_type->canAccessAllMedia() ? 'true' : 'false'));
        
        // Update user type
        $user->user_type = UserType::from($type);
        $user->save();
        
        $this->info("Updated user type to: " . $user->user_type->value);
        $this->info("canAccessAllMedia: " . ($user->user_type->canAccessAllMedia() ? 'true' : 'false'));
        
        return 0;
    }
}
