<?php

namespace App\Console\Commands;

use App\Models\Media;
use App\Services\ThumbnailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DeleteThumbnails extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'media:delete-thumbnails 
                            {--all : Delete all thumbnails for all media}
                            {--media-id=* : Delete thumbnails for specific media IDs}
                            {--size=* : Delete only specific thumbnail sizes}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Delete media thumbnails. Use --all to delete all thumbnails, or specify media IDs and sizes.';

    protected ThumbnailService $thumbnailService;

    public function __construct(ThumbnailService $thumbnailService)
    {
        parent::__construct();
        $this->thumbnailService = $thumbnailService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🗑️  Media Thumbnail Deletion Tool');
        $this->newLine();

        $mediaIds = $this->option('media-id');
        $sizes = $this->option('size');
        $deleteAll = $this->option('all');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No files will actually be deleted');
            $this->newLine();
        }

        // Get media to process
        if ($deleteAll) {
            $media = Media::whereNotNull('id')->get();
            $this->info("Processing all media files ({$media->count()} total)");
        } elseif (!empty($mediaIds)) {
            $media = Media::whereIn('id', $mediaIds)->get();
            $this->info("Processing specific media files: " . implode(', ', $mediaIds));
        } else {
            $this->error('❌ Please specify --all or provide --media-id options');
            return self::FAILURE;
        }

        if ($media->isEmpty()) {
            $this->warn('⚠️  No media files found to process');
            return self::SUCCESS;
        }

        // Get available sizes
        $availableSizes = $this->thumbnailService->getAvailableSizes();
        
        if (!empty($sizes)) {
            // Validate specified sizes
            $invalidSizes = array_diff($sizes, $availableSizes);
            if (!empty($invalidSizes)) {
                $this->error('❌ Invalid thumbnail sizes: ' . implode(', ', $invalidSizes));
                $this->info('Available sizes: ' . implode(', ', $availableSizes));
                return self::FAILURE;
            }
            $sizesToDelete = $sizes;
        } else {
            $sizesToDelete = $availableSizes;
        }

        $this->info('Thumbnail sizes to delete: ' . implode(', ', $sizesToDelete));
        $this->newLine();

        $deletedCount = 0;
        $totalCount = 0;

        $progressBar = $this->output->createProgressBar($media->count());
        $progressBar->start();

        foreach ($media as $mediaItem) {
            foreach ($sizesToDelete as $size) {
                $sizeConfig = $this->thumbnailService->getSizeConfig($size);
                if (!$sizeConfig) {
                    continue;
                }

                $thumbnailPath = $this->thumbnailService->getThumbnailPath($mediaItem, $size, $sizeConfig);
                $totalCount++;

                if (Storage::disk($mediaItem->disk)->exists($thumbnailPath)) {
                    if (!$dryRun) {
                        Storage::disk($mediaItem->disk)->delete($thumbnailPath);
                    }
                    $deletedCount++;
                    
                    if ($dryRun) {
                        $this->line("Would delete: {$thumbnailPath}");
                    }
                }
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        if ($dryRun) {
            $this->info("🔍 DRY RUN RESULTS:");
            $this->info("📊 Total thumbnails that would be deleted: {$deletedCount}");
            $this->info("📊 Total thumbnails checked: {$totalCount}");
        } else {
            $this->info("✅ DELETION COMPLETED:");
            $this->info("🗑️  Thumbnails deleted: {$deletedCount}");
            $this->info("📊 Total thumbnails checked: {$totalCount}");
        }

        $this->newLine();
        $this->info('🎉 Thumbnail deletion process completed!');

        return self::SUCCESS;
    }
}
