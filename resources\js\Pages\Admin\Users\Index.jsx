import React, { useState, useEffect } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Button } from '@/Components/shadcn/ui/button';
import { Plus } from 'lucide-react';
import { ShadcnDataTable } from '@/Components/shadcn/ui/shadcn-data-table';
import { columns } from './columns';
import axios from 'axios';

export default function UsersIndex() {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            setLoading(true);
            const response = await axios.get(route('admin.users.api'));
            setData(response.data.data || []);
        } catch (error) {
            console.error('Error fetching users:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <AppLayout title="User Management">
                <Head title="Users" />
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
                            <p className="text-muted-foreground">
                                Manage users and their access levels
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center justify-center h-64">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
                            <p className="mt-4 text-gray-600">Loading users...</p>
                        </div>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout title="User Management">
            <Head title="Users" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">User Management</h1>
                        <p className="text-muted-foreground">
                            Manage users and their access levels
                        </p>
                    </div>
                </div>

                {/* DataTable */}
                <ShadcnDataTable
                    columns={columns}
                    data={data}
                    filterableColumns={[
                        {
                            id: "user_type",
                            title: "User Type",
                            options: [
                                { label: "Platform Administrator", value: "platform_administrator" },
                                { label: "Brokerage Admin", value: "brokerage_admin" },
                                { label: "Independent Agent", value: "independent_agent" },
                                { label: "Company Agent", value: "company_agent" },
                            ],
                        },
                        {
                            id: "email_verified_at",
                            title: "Email Status",
                            options: [
                                { label: "Verified", value: "verified" },
                                { label: "Unverified", value: "unverified" },
                            ],
                        },
                    ]}
                    addButton={
                        <Link href={route('admin.users.create')}>
                            <Button size="sm" className="h-8">
                                <Plus className="h-4 w-4 mr-2" />
                                Add User
                            </Button>
                        </Link>
                    }
                />
            </div>
        </AppLayout>
    );
}
