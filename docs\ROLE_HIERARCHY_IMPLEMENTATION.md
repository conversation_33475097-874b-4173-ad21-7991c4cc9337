# Role-Based Hierarchy System Implementation

## Overview

Successfully implemented a professional role-based hierarchy system for your Laravel project with the following structure:

```
Platform Administrator (Super Admin)
├── Brokerage Admin (Company Admin)
│   ├── Company Agent (Child Agent under company)
└── Independent Agent (Individual Agent)
```

## Role Specifications Implemented

### 1. Platform Administrator
- **Highest level access** with full system control
- Can manage all users across the platform
- Access to all companies and system-wide settings
- Full permissions across all modules

### 2. Brokerage Admin
- **Company-level administrator** who can manage their own company's agents
- Can add/remove Company Agents under their brokerage
- Access limited to their company scope
- Similar access level to Independent Agent but with company management capabilities

### 3. Independent Agent
- **Individual agent** with similar scope to Brokerage Admin
- Cannot manage other agents (no company management capabilities)
- Operates with personal team/company
- Full access to own listings, media, and reports

### 4. Company Agent
- **Agent working under a specific brokerage/company**
- Limited access compared to other roles
- Can manage own listings and media
- Cannot manage other users or company settings

## Implementation Details

### 1. Updated UserType Enum
- Updated `app/Enums/UserType.php` with new role hierarchy
- Added helper methods for permission checking
- Maintained backward compatibility with deprecated methods

### 2. Spatie Permission Integration
- Installed and configured `spatie/laravel-permission` package
- Created comprehensive permission system with 30+ permissions
- Integrated with existing UserType enum for dual permission checking

### 3. Database Structure
- Updated user_type enum in database to match new hierarchy
- Migrated existing data to new role structure
- Maintained existing team and company relationships

### 4. Role-Based Middleware
Created three middleware classes:
- `RoleBasedAccess`: General role-based access control
- `CompanyScope`: Ensures company-scoped access for relevant roles
- `EnsurePlatformAdmin`: Restricts access to Platform Administrators only

### 5. User Model Enhancements
Added role-based helper methods:
- `isPlatformAdministrator()`, `isBrokerageAdmin()`, etc.
- `canManageAgents()`, `canManageCompanies()`
- `canManageUser($targetUser)` for hierarchical user management
- `getManagedUsers()` for scoped user queries

### 6. Comprehensive Seeding
- `RolePermissionSeeder`: Creates all roles and permissions
- `RoleHierarchySeeder`: Seeds test users for each role with proper relationships

## Permission Structure

### Platform Administration
- `platform.access`, `platform.manage_all_users`, `platform.manage_all_companies`

### User Management
- `users.view_all`, `users.view_own_company`, `users.create`, `users.update`, `users.delete`

### Company/Brokerage Management
- `companies.view_all`, `companies.view_own`, `companies.create`, `companies.update`

### Agent Management
- `agents.view_all`, `agents.view_company`, `agents.create`, `agents.update`, `agents.delete`

### Listings, Media, Reports
- Scoped permissions for viewing, creating, updating, and deleting
- Different access levels based on role hierarchy

## Usage Examples

### Middleware Usage
```php
// Protect routes with role-based access
Route::middleware(['auth', 'role:platform_administrator'])->group(function () {
    Route::get('/admin/platform', [PlatformController::class, 'index']);
});

Route::middleware(['auth', 'company.scope'])->group(function () {
    Route::resource('/company/agents', AgentController::class);
});
```

### Permission Checking
```php
// Check specific permissions
if ($user->can('agents.create')) {
    // User can create agents
}

// Check role-based capabilities
if ($user->canManageAgents()) {
    // User can manage agents based on their role
}

// Check user management hierarchy
if ($user->canManageUser($targetUser)) {
    // User can manage the target user
}
```

### Scoped Queries
```php
// Get users this user can manage
$managedUsers = $user->getManagedUsers()->get();

// Get company scope for filtering
$companyId = $user->getScopeCompanyId();
```

## Test Users Created

The seeder creates the following test users:

1. **Platform Administrator**: `<EMAIL>` (password: `password`)
2. **Brokerage Admin**: `<EMAIL>` (password: `password`)
   - Company: "Elite Realty Brokerage"
3. **Independent Agent**: `<EMAIL>` (password: `password`)
   - Personal Team: "John Smith Real Estate"
4. **Company Agent**: `<EMAIL>` (password: `password`)
   - Under: "Elite Realty Brokerage"

## Key Features

✅ **Hierarchical Access Control**: Platform Admin > Brokerage Admin > Company Agent
✅ **Company Scoping**: Brokerage Admins restricted to their company
✅ **Flexible Permissions**: Granular permission system with 30+ permissions
✅ **Backward Compatibility**: Existing code continues to work with deprecated methods
✅ **Team Integration**: Proper integration with Laravel Jetstream teams
✅ **Middleware Protection**: Route-level access control
✅ **User Management Hierarchy**: Clear rules for who can manage whom

## Next Steps

1. **Update Controllers**: Apply role-based middleware to your existing routes
2. **Frontend Integration**: Update React components to check user permissions
3. **Testing**: Create comprehensive tests for your specific use cases
4. **Documentation**: Update API documentation with new permission requirements

The role hierarchy system is now fully implemented and ready for use in your Laravel/Inertia.js application!
