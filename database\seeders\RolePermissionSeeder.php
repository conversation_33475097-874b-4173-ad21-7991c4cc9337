<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Enums\UserType;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Define all permissions for the role hierarchy
        $permissions = [
            // Platform Administration
            'platform.access' => 'Access platform administration features',
            'platform.manage_all_users' => 'Manage all users across the platform',
            'platform.manage_all_companies' => 'Manage all companies/brokerages',
            'platform.system_settings' => 'Access system-wide settings',
            'platform.view_analytics' => 'View platform-wide analytics',

            // User Management
            'users.view_all' => 'View all users',
            'users.view_own_company' => 'View users in own company',
            'users.create' => 'Create new users',
            'users.update' => 'Update user information',
            'users.delete' => 'Delete users',
            'users.manage_roles' => 'Assign and manage user roles',

            // Company/Brokerage Management
            'companies.view_all' => 'View all companies',
            'companies.view_own' => 'View own company information',
            'companies.create' => 'Create new companies',
            'companies.update' => 'Update company information',
            'companies.delete' => 'Delete companies',
            'companies.manage_settings' => 'Manage company settings',

            // Agent Management
            'agents.view_all' => 'View all agents',
            'agents.view_company' => 'View agents in own company',
            'agents.create' => 'Add new agents to company',
            'agents.update' => 'Update agent information',
            'agents.delete' => 'Remove agents',
            'agents.assign_to_company' => 'Assign agents to companies',

            // Listings Management
            'listings.view_all' => 'View all listings',
            'listings.view_company' => 'View company listings',
            'listings.view_own' => 'View own listings',
            'listings.create' => 'Create new listings',
            'listings.update' => 'Update listings',
            'listings.delete' => 'Delete listings',
            'listings.publish' => 'Publish/unpublish listings',

            // Media Management
            'media.view_all' => 'View all media files',
            'media.view_company' => 'View company media files',
            'media.view_own' => 'View own media files',
            'media.upload' => 'Upload media files',
            'media.update' => 'Update media information',
            'media.delete' => 'Delete media files',
            'media.manage_collections' => 'Manage media collections',

            // Reports and Analytics
            'reports.view_all' => 'View all reports',
            'reports.view_company' => 'View company reports',
            'reports.view_own' => 'View own reports',
            'reports.export' => 'Export reports',

            // Settings
            'settings.platform' => 'Manage platform settings',
            'settings.company' => 'Manage company settings',
            'settings.profile' => 'Manage own profile settings',
        ];

        // Create permissions
        foreach (array_keys($permissions) as $name) {
            Permission::firstOrCreate(['name' => $name]);
        }

        // Create roles based on UserType enum
        $this->createPlatformAdministratorRole();
        $this->createBrokerageAdminRole();
        $this->createIndependentAgentRole();
        $this->createCompanyAgentRole();
    }

    private function createPlatformAdministratorRole(): void
    {
        $role = Role::firstOrCreate([
            'name' => UserType::PLATFORM_ADMINISTRATOR->value,
        ]);

        // Platform Administrator gets ALL permissions
        $role->syncPermissions(Permission::all());
    }

    private function createBrokerageAdminRole(): void
    {
        $role = Role::firstOrCreate([
            'name' => UserType::BROKERAGE_ADMIN->value,
        ]);

        $permissions = [
            // User management within company
            'users.view_own_company',
            'users.create',
            'users.update',
            'users.delete',

            // Company management
            'companies.view_own',
            'companies.update',
            'companies.manage_settings',

            // Agent management
            'agents.view_company',
            'agents.create',
            'agents.update',
            'agents.delete',
            'agents.assign_to_company',

            // Listings
            'listings.view_company',
            'listings.view_own',
            'listings.create',
            'listings.update',
            'listings.delete',
            'listings.publish',

            // Media
            'media.view_company',
            'media.view_own',
            'media.upload',
            'media.update',
            'media.delete',
            'media.manage_collections',

            // Reports
            'reports.view_company',
            'reports.view_own',
            'reports.export',

            // Settings
            'settings.company',
            'settings.profile',
        ];

        $role->syncPermissions($permissions);
    }

    private function createIndependentAgentRole(): void
    {
        $role = Role::firstOrCreate([
            'name' => UserType::INDEPENDENT_AGENT->value,
        ]);

        $permissions = [
            // Similar to Brokerage Admin but without company agent management
            'companies.view_own',
            'companies.update',
            'companies.manage_settings',

            // Listings
            'listings.view_own',
            'listings.create',
            'listings.update',
            'listings.delete',
            'listings.publish',

            // Media
            'media.view_own',
            'media.upload',
            'media.update',
            'media.delete',
            'media.manage_collections',

            // Reports
            'reports.view_own',
            'reports.export',

            // Settings
            'settings.profile',
        ];

        $role->syncPermissions($permissions);
    }

    private function createCompanyAgentRole(): void
    {
        $role = Role::firstOrCreate([
            'name' => UserType::COMPANY_AGENT->value,
        ]);

        $permissions = [
            // Basic agent permissions
            'listings.view_own',
            'listings.create',
            'listings.update',
            'listings.delete',
            'listings.publish',

            // Media
            'media.view_own',
            'media.upload',
            'media.update',
            'media.delete',

            // Reports
            'reports.view_own',

            // Settings
            'settings.profile',
        ];

        $role->syncPermissions($permissions);
    }
}
