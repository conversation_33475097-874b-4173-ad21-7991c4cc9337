<?php

declare(strict_types=1);

use Lara<PERSON>\Fortify\Features;
use Lara<PERSON>\Jetstream\Jetstream;

test('registration screen can be rendered', function (): void {
    $response = $this->get('/register');

    $response->assertStatus(200);
})->skip(fn (): bool => ! Features::enabled(Features::registration()), 'Registration support is not enabled.');

test('new users can register', function (): void {
    $response = $this->post('/register', [
        'name' => fake()->name(),
        'email' => fake()->unique()->safeEmail(),
        'password' => 'password',
        'password_confirmation' => 'password',
        'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature(),
    ]);

    $this->assertAuthenticated();
    $response->assertRedirect(route('dashboard', absolute: false));
});
