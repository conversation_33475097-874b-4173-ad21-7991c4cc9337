import FormSection from '@/Components/FormSection'
import InputError from '@/Components/InputError'
import MediaImageSelector from '@/Components/MediaImageSelector'
import { Button } from '@/Components/shadcn/ui/button'
import { Input } from '@/Components/shadcn/ui/input'
import { Label } from '@/Components/shadcn/ui/label'
import { Link, router, useForm, usePage } from '@inertiajs/react'
import { memo, useRef, useState, useEffect } from 'react'
import { toast } from 'sonner'
import { route } from 'ziggy-js'

export default memo(({ user }) => {
  const { props: { jetstream } } = usePage()
  const [verificationLinkSent, setVerificationLinkSent] = useState(false)
  const [photoPreview, setPhotoPreview] = useState(null)
  const [selectedMedia, setSelectedMedia] = useState(null)
  const photoRef = useRef(null)

  const form = useForm({
    _method: 'PUT',
    name: user.name,
    email: user.email,
    phone: user.phone || '',
    company_name: user.company_name || '',
    license_number: user.license_number || '',
    license_state: user.license_state || '',
    license_expiry: user.license_expiry || '',
    profile_image_id: user.profile_image_id || null,
    photo: null,
  })

  // Load selected media when component mounts or user changes
  useEffect(() => {
    if (user.profile_image_id && user.profile_image) {
      setSelectedMedia(user.profile_image);
    }
  }, [user.profile_image_id, user.profile_image]);

  const clearPhotoFileInput = () => {
    if (photoRef.current?.value) {
      photoRef.current.value = null
    }
  }

  const handleMediaSelect = (mediaId, mediaObject = null) => {
    form.setData('profile_image_id', mediaId);

    // Update selectedMedia state for immediate preview
    if (mediaId && mediaObject) {
      setSelectedMedia(mediaObject);
    } else if (!mediaId) {
      setSelectedMedia(null);
    }
  };

  const updateProfileInformation = (e) => {
    e.preventDefault()

    // If using old photo upload method
    if (photoRef.current?.files[0]) {
      form.setData('photo', photoRef.current.files[0])
    }

    form.post(route('user-profile-information.update'), {
      errorBag: 'updateProfileInformation',
      preserveScroll: true,
      onSuccess: () => {
        clearPhotoFileInput()
        toast.success('Profile information updated')
      },
    })
  }

  const sendEmailVerification = () => {
    setVerificationLinkSent(true)
  }

  const selectNewPhoto = () => {
    photoRef.current?.click()
  }

  const updatePhotoPreview = () => {
    const photo = photoRef.current?.files[0]
    if (!photo)
      return

    const reader = new FileReader()
    reader.onload = (e) => {
      setPhotoPreview(e.target.result)
    }
    reader.readAsDataURL(photo)
  }

  const deletePhoto = () => {
    router.delete(route('current-user-photo.destroy'), {
      preserveScroll: true,
      onSuccess: () => {
        setPhotoPreview(null)
        clearPhotoFileInput()
        toast.success('Photo deleted')
      },
    })
  }

  return (
    <FormSection
      onSubmit={updateProfileInformation}
      title="Profile Information"
      description={`Update your ${user.user_type === 'brokerage_admin' ? 'company' : user.user_type === 'independent_agent' ? 'agent' : 'account'} profile information.`}
      form={(
        <>
          {/* Profile Photo */}
          {jetstream.managesProfilePhotos && (
            <div className="col-span-6 sm:col-span-4">
              <MediaImageSelector
                label="Profile Photo"
                value={form.data.profile_image_id}
                onChange={handleMediaSelect}
                selectedMedia={selectedMedia}
                accept="image/*"
                placeholder="Select a profile photo"
                description="Choose an image from your media library or upload a new one"
                error={form.errors.profile_image_id || form.errors.photo}
              />

              {/* Keep the old photo upload as fallback */}
              <input
                ref={photoRef}
                type="file"
                className="hidden"
                onChange={updatePhotoPreview}
              />

              {/* Legacy photo preview for old uploads */}
              {!form.data.profile_image_id && !photoPreview && user.profile_photo_url && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-2">Current photo (legacy):</p>
                  <img
                    src={user.profile_photo_url}
                    alt={user.name}
                    className="size-20 rounded-full object-cover"
                  />
                  <div className="mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={selectNewPhoto}
                    >
                      Upload New Photo
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={deletePhoto}
                      className="ml-2"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              )}

              {photoPreview && (
                <div className="mt-4">
                  <p className="text-sm text-muted-foreground mb-2">New photo preview:</p>
                  <span
                    className="block size-20 rounded-full bg-cover bg-center bg-no-repeat"
                    style={{ backgroundImage: `url('${photoPreview}')` }}
                  />
                </div>
              )}
            </div>
          )}

          {/* Name */}
          <div className="col-span-6 sm:col-span-4">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              type="text"
              value={form.data.name}
              onChange={e => form.setData('name', e.target.value)}
              className="mt-1 block w-full"
              required
              autoComplete="name"
            />
            <InputError message={form.errors.name} className="mt-2" />
          </div>

          {/* Email */}
          <div className="col-span-6 sm:col-span-4">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={form.data.email}
              disabled
              className="mt-1 block w-full"
              required
              autoComplete="username"
            />
            <InputError message={form.errors.email} className="mt-2" />

            {jetstream.hasEmailVerification && !user.email_verified_at && (
              <div>
                <p className="mt-2 text-sm">
                  Your email address is unverified.
                  <Link
                    href={route('verification.send')}
                    method="post"
                    type="button"
                    as="button"
                    onClick={sendEmailVerification}
                    className="rounded-md text-sm underline focus:outline-hidden focus:ring-2 focus:ring-offset-2"
                  >
                    Click here to re-send the verification email.
                  </Link>
                </p>

                {verificationLinkSent && (
                  <div className="mt-2 text-sm font-medium">
                    A new verification link has been sent to your email address.
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Phone - All user types */}
          <div className="col-span-6 sm:col-span-4">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              type="tel"
              value={form.data.phone}
              onChange={e => form.setData('phone', e.target.value)}
              className="mt-1 block w-full"
              placeholder="(*************"
              autoComplete="tel"
            />
            <InputError message={form.errors.phone} className="mt-2" />
          </div>

          {/* Company Name - Brokerage Admin only */}
          {user.user_type === 'brokerage_admin' && (
            <div className="col-span-6 sm:col-span-4">
              <Label htmlFor="company_name">Company Name</Label>
              <Input
                id="company_name"
                type="text"
                value={form.data.company_name}
                onChange={e => form.setData('company_name', e.target.value)}
                className="mt-1 block w-full"
                placeholder="Your Real Estate Company"
                autoComplete="organization"
              />
              <InputError message={form.errors.company_name} className="mt-2" />
            </div>
          )}

          {/* License Information - Individual Agent only */}
          {user.user_type === 'individual_agent' && (
            <>
              <div className="col-span-6 sm:col-span-4">
                <Label htmlFor="license_number">License Number</Label>
                <Input
                  id="license_number"
                  type="text"
                  value={form.data.license_number}
                  onChange={e => form.setData('license_number', e.target.value)}
                  className="mt-1 block w-full"
                  placeholder="RE123456789"
                />
                <InputError message={form.errors.license_number} className="mt-2" />
              </div>

              <div className="col-span-6 sm:col-span-2">
                <Label htmlFor="license_state">License State</Label>
                <Input
                  id="license_state"
                  type="text"
                  value={form.data.license_state}
                  onChange={e => form.setData('license_state', e.target.value)}
                  className="mt-1 block w-full"
                  placeholder="CA"
                  maxLength="2"
                />
                <InputError message={form.errors.license_state} className="mt-2" />
              </div>

              <div className="col-span-6 sm:col-span-2">
                <Label htmlFor="license_expiry">License Expiry</Label>
                <Input
                  id="license_expiry"
                  type="date"
                  value={form.data.license_expiry}
                  onChange={e => form.setData('license_expiry', e.target.value)}
                  className="mt-1 block w-full"
                />
                <InputError message={form.errors.license_expiry} className="mt-2" />
              </div>
            </>
          )}
        </>
      )}
      actions={
        <Button disabled={form.processing}>Save</Button>
      }
    />
  )
})
