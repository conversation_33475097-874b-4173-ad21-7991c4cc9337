import React, { useState, useCallback } from 'react';
import { Head, router, usePage } from '@inertiajs/react';
import axios from 'axios';
import { toast } from 'sonner';
import { route } from 'ziggy-js';

import AppLayout from '@/layouts/AppLayout';
import { Button } from '@/components/shadcn/ui/button';
import { Input } from '@/components/shadcn/ui/input';

import { Pagination } from '@/components/shadcn/ui/pagination';

import {
    Upload,
    FolderPlus
} from 'lucide-react';
import { Cross2Icon, PlusCircledIcon } from '@radix-ui/react-icons';
import { Badge } from '@/Components/shadcn/ui/badge';
import { Separator } from '@/Components/shadcn/ui/separator';
import {
    Command,
    CommandGroup,
    CommandItem,
    CommandList,
} from '@/Components/shadcn/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/Components/shadcn/ui/popover';


import FileManager from './Components/FileManager';
import MediaUploader from './Components/MediaUploader';
import MediaBreadcrumbs from './Components/MediaBreadcrumbs';
import CreateFolderModal from './Components/CreateFolderModal';
import MediaDetailsModal from './Components/MediaDetailsModal';
import EditFolderModal from './Components/EditFolderModal';
import MoveModal from './Components/MoveModal';
import { ConfirmDialog } from '@/Components/shadcn/ui/confirm-dialog';

// Custom Media Type Filter Component
function MediaTypeFilter({ value, onValueChange, options, title }) {
    const selectedValues = value && value !== 'all' ? [value] : [];

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 border-dashed">
                    <PlusCircledIcon className="mr-2 h-4 w-4" />
                    {title}
                    {selectedValues.length > 0 && (
                        <>
                            <Separator orientation="vertical" className="mx-2 h-4" />
                            <Badge
                                variant="secondary"
                                className="rounded-sm px-1 font-normal lg:hidden"
                            >
                                {selectedValues.length}
                            </Badge>
                            <div className="hidden space-x-1 lg:flex">
                                {options
                                    .filter((option) => selectedValues.includes(option.value))
                                    .map((option) => (
                                        <Badge
                                            variant="secondary"
                                            key={option.value}
                                            className="rounded-sm px-1 font-normal"
                                        >
                                            {option.label}
                                        </Badge>
                                    ))}
                            </div>
                        </>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
                <Command>
                    <CommandList>
                        <CommandGroup>
                            {/* All Types option */}
                            <CommandItem
                                onSelect={() => onValueChange('all')}
                            >
                                <span>All Types</span>
                            </CommandItem>
                            {options.map((option) => {
                                return (
                                    <CommandItem
                                        key={option.value}
                                        onSelect={() => onValueChange(option.value)}
                                    >
                                        <span>{option.label}</span>
                                    </CommandItem>
                                );
                            })}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
}

// Custom Sort Filter Component
function MediaSortFilter({ value, onValueChange, options, title }) {
    const selectedOption = options.find(option => option.value === value);

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 border-dashed">
                    <PlusCircledIcon className="mr-2 h-4 w-4" />
                    {title}
                    {selectedOption && (
                        <>
                            <Separator orientation="vertical" className="mx-2 h-4" />
                            <Badge
                                variant="secondary"
                                className="rounded-sm px-1 font-normal"
                            >
                                {selectedOption.label}
                            </Badge>
                        </>
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[200px] p-0" align="start">
                <Command>
                    <CommandList>
                        <CommandGroup>
                            {options.map((option) => {
                                return (
                                    <CommandItem
                                        key={option.value}
                                        onSelect={() => onValueChange(option.value)}
                                    >
                                        <span>{option.label}</span>
                                    </CommandItem>
                                );
                            })}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
}

export default function MediaIndex() {
    const { media, folders, currentFolder, breadcrumbs, filters, userPermissions } = usePage().props;

    const [showUploader, setShowUploader] = useState(false);
    const [showCreateFolder, setShowCreateFolder] = useState(false);
    const [showMediaDetails, setShowMediaDetails] = useState(false);
    const [showFolderEdit, setShowFolderEdit] = useState(false);
    const [showMoveModal, setShowMoveModal] = useState(false);
    const [selectedMediaItem, setSelectedMediaItem] = useState(null);
    const [selectedFolderItem, setSelectedFolderItem] = useState(null);
    const [moveItem, setMoveItem] = useState(null);
    const [moveItemType, setMoveItemType] = useState('media');

    // State for delete confirmation
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [deleteItem, setDeleteItem] = useState(null);

    const [searchQuery, setSearchQuery] = useState(filters.search || '');
    const [currentFilters, setCurrentFilters] = useState(filters);

    // Sync local state with props when filters change from backend
    React.useEffect(() => {
        setCurrentFilters(filters);
    }, [filters]);

    // Check if filters are applied
    const isFiltered = (currentFilters.type && currentFilters.type !== 'all') ||
                      (currentFilters.search && currentFilters.search !== '') ||
                      (currentFilters.sort_by !== 'created_at' || currentFilters.sort_order !== 'desc');

    // Clear all filters
    const clearFilters = () => {
        const newFilters = {
            search: undefined,
            type: undefined,
            sort_by: 'created_at',
            sort_order: 'desc',
            page: 1
        };

        setCurrentFilters(newFilters);
        setSearchQuery('');

        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        router.get(route(routeName), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };





    // Handle search
    const handleSearch = useCallback((value) => {
        setSearchQuery(value);

        const params = {
            ...currentFilters,
            search: value || undefined,
            page: 1
        };

        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        router.get(route(routeName), params, {
            preserveState: true,
            preserveScroll: true,
        });
    }, [currentFilters]);

    // Handle filter changes
    const handleFilterChange = useCallback((key, value) => {
        const newFilters = {
            ...currentFilters,
            [key]: value === 'all' ? undefined : value,
            page: 1
        };

        setCurrentFilters(newFilters);

        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        router.get(route(routeName), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    }, [currentFilters]);

    // Handle sorting
    const handleSort = useCallback((sortBy, sortOrder) => {
        const newFilters = {
            ...currentFilters,
            sort_by: sortBy,
            sort_order: sortOrder,
            page: 1
        };

        setCurrentFilters(newFilters);

        const isCompanyContext = window.location.pathname.includes('/company/');
        const routeName = isCompanyContext ? 'company.media.index' : 'admin.media.index';

        router.get(route(routeName), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    }, [currentFilters]);





    // Handle upload complete
    const handleUploadComplete = useCallback(() => {
        setShowUploader(false);
        router.reload({ only: ['media'] });
        toast.success('Files uploaded successfully');
    }, []);

    // Handle media click (show details)
    const handleMediaClick = useCallback((mediaItem) => {
        setSelectedMediaItem(mediaItem);
        setShowMediaDetails(true);
    }, []);

    // Handle media edit
    const handleMediaEdit = useCallback((mediaItem) => {
        setSelectedMediaItem(mediaItem);
        setShowMediaDetails(true);
        // Set edit mode after modal opens
        setTimeout(() => {
            const editButton = document.querySelector('[data-edit-button]');
            if (editButton) {
                editButton.click();
            }
        }, 100);
    }, []);

    // Handle folder edit
    const handleFolderEdit = useCallback((folder) => {
        setSelectedFolderItem(folder);
        setShowFolderEdit(true);
    }, []);

    // Handle copy URL
    const handleCopyUrl = useCallback(async (url) => {
        try {
            await navigator.clipboard.writeText(url);
            toast.success('URL copied to clipboard');
        } catch (err) {
            toast.error('Failed to copy URL');
        }
    }, []);

    // Handle move
    const handleMediaMove = useCallback((mediaItem) => {
        setMoveItem(mediaItem);
        setMoveItemType('media');
        setShowMoveModal(true);
    }, []);

    const handleFolderMove = useCallback((folder) => {
        setMoveItem(folder);
        setMoveItemType('folder');
        setShowMoveModal(true);
    }, []);

    // Handle delete
    const handleMediaDelete = useCallback((mediaItem) => {
        setDeleteItem(mediaItem);
        setShowDeleteConfirm(true);
    }, []);

    const confirmDelete = async () => {
        if (!deleteItem) return;

        try {
            const isCompanyContext = window.location.pathname.includes('/company/');
            const routeName = isCompanyContext ? 'company.media.destroy' : 'admin.media.destroy';

            await axios.delete(route(routeName, deleteItem.id));

            toast.success('File deleted successfully');
            router.reload();
        } catch (error) {
            toast.error(error.response?.data?.message || 'Failed to delete file');
        }
    };

    return (
        <AppLayout title="Media Library">
            <Head title="Media Library" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight">Media Library</h1>
                        <p className="text-muted-foreground">
                            Manage your media files and folders
                        </p>
                    </div>
                </div>

                {/* Breadcrumbs */}
                <MediaBreadcrumbs
                    breadcrumbs={breadcrumbs}
                    currentFolder={currentFolder}
                />

                {/* Toolbar - DataTable Style */}
                <div className="flex items-center justify-between">
                    <div className="flex flex-1 items-center space-x-2">
                        <Input
                            placeholder="Filter media..."
                            value={searchQuery}
                            onChange={(e) => handleSearch(e.target.value)}
                            className="h-8 w-[150px] lg:w-[250px]"
                        />
                        <MediaTypeFilter
                            value={currentFilters.type || 'all'}
                            onValueChange={(value) => handleFilterChange('type', value)}
                            title="Type"
                            options={[
                                { label: "Images", value: "image" },
                                { label: "Videos", value: "video" },
                                { label: "Audio", value: "audio" },
                                { label: "Documents", value: "document" },
                            ]}
                        />
                        <MediaSortFilter
                            value={`${currentFilters.sort_by || 'created_at'}-${currentFilters.sort_order || 'desc'}`}
                            onValueChange={(value) => {
                                const [sortBy, sortOrder] = value.split('-');
                                handleSort(sortBy, sortOrder);
                            }}
                            title="Sort"
                            options={[
                                { label: "Newest First", value: "created_at-desc" },
                                { label: "Oldest First", value: "created_at-asc" },
                                { label: "Name A-Z", value: "original_filename-asc" },
                                { label: "Name Z-A", value: "original_filename-desc" },
                                { label: "Largest First", value: "size-desc" },
                                { label: "Smallest First", value: "size-asc" },
                            ]}
                        />
                        {isFiltered && (
                            <Button
                                variant="ghost"
                                onClick={clearFilters}
                                className="h-8 px-2 lg:px-3"
                            >
                                Reset
                                <Cross2Icon className="ml-2 h-4 w-4" />
                            </Button>
                        )}
                    </div>
                    <div className="flex items-center space-x-2">
                        {userPermissions.canUpload && (
                            <Button
                                variant="outline"
                                size="sm"
                                className="h-8"
                                onClick={() => setShowCreateFolder(true)}
                            >
                                <FolderPlus className="h-4 w-4 mr-2" />
                                Create Folder
                            </Button>
                        )}
                        {userPermissions.canUpload && (
                            <Button
                                size="sm"
                                className="h-8"
                                onClick={() => setShowUploader(true)}
                            >
                                <Upload className="h-4 w-4 mr-2" />
                                Upload Files
                            </Button>
                        )}
                    </div>
                </div>

                {/* File Manager */}
                <FileManager
                    folders={folders}
                    media={media}
                    onMediaClick={handleMediaClick}
                    onMediaEdit={handleMediaEdit}
                    onFolderEdit={handleFolderEdit}
                    onMediaMove={handleMediaMove}
                    onFolderMove={handleFolderMove}
                    onCopyUrl={handleCopyUrl}
                />

                {/* Pagination */}
                {media.last_page > 1 && (
                    <div className="flex justify-center mt-6">
                        <Pagination
                            currentPage={media.current_page}
                            totalPages={media.last_page}
                            onPageChange={(page) => {
                                const url = new URL(window.location);
                                url.searchParams.set('page', page);
                                router.visit(url.toString());
                            }}
                        />
                    </div>
                )}

                {/* Modals */}
                <MediaUploader
                    isOpen={showUploader}
                    onClose={() => setShowUploader(false)}
                    onUploadComplete={handleUploadComplete}
                    uploadRoute={window.location.pathname.includes('/company/')
                        ? route('company.media.store')
                        : route('admin.media.store')
                    }
                    currentFolder={currentFolder}
                />

                <CreateFolderModal
                    isOpen={showCreateFolder}
                    onClose={() => setShowCreateFolder(false)}
                    currentFolder={currentFolder}
                    onFolderCreated={() => {
                        setShowCreateFolder(false);
                        router.reload();
                    }}
                />

                <MediaDetailsModal
                    isOpen={showMediaDetails}
                    onClose={() => {
                        setShowMediaDetails(false);
                        setSelectedMediaItem(null);
                    }}
                    media={selectedMediaItem}
                    userPermissions={userPermissions}
                    onMove={(mediaItem) => handleMediaMove(mediaItem)}
                    onDelete={(mediaItem) => handleMediaDelete(mediaItem)}
                />

                <EditFolderModal
                    isOpen={showFolderEdit}
                    onClose={() => {
                        setShowFolderEdit(false);
                        setSelectedFolderItem(null);
                    }}
                    folder={selectedFolderItem}
                    onFolderUpdated={() => {
                        setShowFolderEdit(false);
                        setSelectedFolderItem(null);
                        router.reload();
                    }}
                />

                <MoveModal
                    isOpen={showMoveModal}
                    onClose={() => {
                        setShowMoveModal(false);
                        setMoveItem(null);
                    }}
                    item={moveItem}
                    itemType={moveItemType}
                    folders={folders}
                    currentFolder={currentFolder}
                    onMoveComplete={() => {
                        setShowMoveModal(false);
                        setMoveItem(null);
                        router.reload();
                    }}
                />

                {/* Delete Confirmation Dialog */}
                <ConfirmDialog
                    isOpen={showDeleteConfirm}
                    onClose={() => {
                        setShowDeleteConfirm(false);
                        setDeleteItem(null);
                    }}
                    onConfirm={confirmDelete}
                    title="Delete File?"
                    description={`Are you sure you want to delete "${deleteItem?.original_filename}"? This action cannot be undone.`}
                    confirmText="Delete"
                    cancelText="Cancel"
                    variant="destructive"
                />
            </div>
        </AppLayout>
    );
}
