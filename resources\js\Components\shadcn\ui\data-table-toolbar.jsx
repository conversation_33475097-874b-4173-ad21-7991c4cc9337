"use client"

import { Cross2Icon } from "@radix-ui/react-icons"

import { Button } from "@/Components/shadcn/ui/button"
import { Input } from "@/Components/shadcn/ui/input"
import { DataTableViewOptions } from "@/Components/shadcn/ui/data-table-view-options"
import { DataTableFacetedFilter } from "@/Components/shadcn/ui/data-table-faceted-filter"

export function DataTableToolbar({ table, filterableColumns = [], addButton }) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter..."
          value={(table.getColumn("name")?.getFilterValue()) ?? ""}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {filterableColumns.map((column) =>
          table.getColumn(column.id) ? (
            <DataTableFacetedFilter
              key={column.id}
              column={table.getColumn(column.id)}
              title={column.title}
              options={column.options}
            />
          ) : null
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <DataTableViewOptions table={table} />
        {addButton && addButton}
      </div>
    </div>
  )
}
