# 🌉 Inertia.js + React Integration Guide

## What is Inertia.js?

Inertia.js is a modern approach to building single-page applications (SPAs) using classic server-side routing and controllers. It acts as a bridge between your Laravel backend and React frontend, eliminating the need for separate API endpoints while providing a smooth SPA experience.

## Key Concepts

### Traditional vs. Inertia.js Approach

**Traditional SPA:**

```
Frontend (React) ←→ API Endpoints ←→ Laravel Backend
     ↓                    ↓                ↓
State Management    JSON Responses    Controllers
Route Handling      Authentication    Models
```

**Inertia.js Approach:**

```
React Components ←→ Inertia.js ←→ Laravel Controllers
     ↓                 ↓              ↓
UI Rendering      Bridge Layer    Business Logic
Props Handling    Page Routing    Data Preparation
```

### Benefits of Inertia.js

1. **No API Required** - Controllers return React components directly
2. **Server-Side Routing** - Use Laravel routes, not client-side routing
3. **Automatic CSRF Protection** - Built into every request
4. **SEO Friendly** - Server-side rendering for initial page load
5. **Simple Data Flow** - Props flow directly from controllers to components

## How Inertia.js Works

### 1. Initial Page Load (Server-Side Rendered)

```php
// Laravel Route
Route::get('/dashboard', [DashboardController::class, 'index']);

// Controller
public function index()
{
    return Inertia::render('Dashboard', [
        'user' => auth()->user(),
        'stats' => $this->getDashboardStats()
    ]);
}
```

```html
<!-- Server returns full HTML page -->
<!doctype html>
<html>
  <head>
    <script>
      window.page = {
          component: 'Dashboard',
          props: { user: {...}, stats: {...} },
          url: '/dashboard'
      }
    </script>
  </head>
  <body>
    <div id="app" data-page="...">
      <!-- React component rendered server-side -->
    </div>
  </body>
</html>
```

### 2. Subsequent Navigation (Client-Side)

```jsx
// User clicks a link
<Link href="/agents">View Agents</Link>

// Inertia.js automatically:
// 1. Intercepts the click
// 2. Makes AJAX request with X-Inertia header
// 3. Receives JSON response with new component + props
// 4. Renders new component without page reload
```

```javascript
// AJAX Request
GET /agents
Headers: {
    'X-Inertia': true,
    'X-Inertia-Version': 'abc123'
}

// Response
{
    component: 'Agents/Index',
    props: { agents: [...], filters: {...} },
    url: '/agents'
}
```

## Core Inertia.js APIs

### 1. Rendering Pages (Laravel)

```php
use Inertia\Inertia;

// Basic page render
return Inertia::render('Dashboard');

// With props
return Inertia::render('Dashboard', [
    'user' => $user,
    'stats' => $stats
]);

// With lazy props (computed only when needed)
return Inertia::render('Dashboard', [
    'user' => $user,
    'expensive_data' => Inertia::lazy(fn () => $this->getExpensiveData())
]);
```

### 2. Navigation (React)

```jsx
import { Link, router } from '@inertiajs/react'

// Declarative navigation
<Link href="/dashboard">Dashboard</Link>
<Link href="/agents" method="post" data={{ name: 'John' }}>
    Create Agent
</Link>

// Programmatic navigation
router.visit('/dashboard')
router.post('/agents', { name: 'John' })
router.reload() // Refresh current page
router.reload({ only: ['stats'] }) // Refresh specific props
```

### 3. Forms (React)

```jsx
import { useForm } from '@inertiajs/react'

export default function CreateAgent() {
  const { data, setData, post, processing, errors, reset } = useForm({
    name: '',
    email: '',
    license_number: ''
  })

  const submit = (e) => {
    e.preventDefault()
    post('/agents', {
      onSuccess: () => reset(),
      onError: errors => console.log(errors)
    })
  }

  return (
    <form onSubmit={submit}>
      <input
        value={data.name}
        onChange={e => setData('name', e.target.value)}
      />
      {errors.name && <span>{errors.name}</span>}

      <button disabled={processing}>
        {processing ? 'Creating...' : 'Create'}
      </button>
    </form>
  )
}
```

### 4. Accessing Page Data (React)

```jsx
import { usePage } from '@inertiajs/react'

export default function SomeComponent() {
  const { props, component, url } = usePage()

  // Access shared data (from HandleInertiaRequests middleware)
  const user = props.auth.user
  const flashMessage = props.flash.success

  // Access page-specific props
  const agents = props.agents

  return (
    <div>
      <h1>
        Current page:
        {component}
      </h1>
      <p>
        Current URL:
        {url}
      </p>
      {flashMessage && <Alert>{flashMessage}</Alert>}
    </div>
  )
}
```

## Advanced Patterns

### 1. Partial Reloads

```jsx
// Reload only specific props
router.reload({ only: ['stats', 'notifications'] })

// Reload everything except specific props
router.reload({ except: ['user'] })

// Useful for live data updates
useEffect(() => {
  const interval = setInterval(() => {
    router.reload({ only: ['stats'] })
  }, 30000)

  return () => clearInterval(interval)
}, [])
```

### 2. File Uploads

```jsx
import { useForm } from '@inertiajs/react'

export default function UploadForm() {
  const { data, setData, post, progress } = useForm({
    avatar: null
  })

  const submit = (e) => {
    e.preventDefault()
    post('/upload', {
      forceFormData: true, // Force multipart/form-data
      onProgress: (progress) => {
        console.log(`Upload progress: ${progress.percentage}%`)
      }
    })
  }

  return (
    <form onSubmit={submit}>
      <input
        type="file"
        onChange={e => setData('avatar', e.target.files[0])}
      />
      {progress && (
        <div>
          Upload progress:
          {progress.percentage}
          %
        </div>
      )}
      <button type="submit">Upload</button>
    </form>
  )
}
```

### 3. Global Event Handling

```jsx
import { router } from '@inertiajs/react'
import { useEffect } from 'react'

export default function App() {
  useEffect(() => {
    // Global loading state
    const handleStart = () => setLoading(true)
    const handleFinish = () => setLoading(false)

    router.on('start', handleStart)
    router.on('finish', handleFinish)

    // Global error handling
    router.on('error', (errors) => {
      console.error('Navigation error:', errors)
    })

    return () => {
      router.off('start', handleStart)
      router.off('finish', handleFinish)
    }
  }, [])

  return <div>...</div>
}
```

### 4. Conditional Rendering Based on Props

```jsx
export default function Dashboard({ user, canManageAgents, stats }) {
  return (
    <AppLayout title="Dashboard">
      <h1>
        Welcome,
        {user.name}
      </h1>

      {canManageAgents && (
        <section>
          <h2>Agent Management</h2>
          <Link href="/agents">Manage Agents</Link>
        </section>
      )}

      <StatsGrid stats={stats} />
    </AppLayout>
  )
}
```

## Best Practices

### 1. Controller Organization

```php
class DashboardController extends Controller
{
    public function index()
    {
        return Inertia::render('Dashboard', [
            // Always include user for authenticated pages
            'user' => auth()->user()->load('teams'),

            // Use descriptive prop names
            'dashboardStats' => $this->getDashboardStats(),
            'recentActivity' => $this->getRecentActivity(),

            // Include permissions for conditional rendering
            'can' => [
                'manage_agents' => auth()->user()->can('manage-agents'),
                'view_reports' => auth()->user()->can('view-reports'),
            ]
        ]);
    }

    private function getDashboardStats(): array
    {
        return [
            'total_agents' => Agent::count(),
            'active_listings' => Listing::active()->count(),
            'pending_leads' => Lead::pending()->count(),
        ];
    }
}
```

### 2. Component Structure

```jsx
// Good: Clear prop destructuring
export default function AgentsList({ agents, filters, pagination }) {
    return (
        <AppLayout title="Agents">
            <div className="space-y-6">
                <AgentsHeader />
                <AgentsFilters filters={filters} />
                <AgentsTable agents={agents.data} />
                <Pagination links={pagination.links} />
            </div>
        </AppLayout>
    )
}

// Better: Use TypeScript-style prop documentation
/**
 * @param {Object} props
 * @param {Array} props.agents - Paginated agent data
 * @param {Object} props.filters - Current filter values
 * @param {Object} props.pagination - Pagination metadata
 */
export default function AgentsList({ agents, filters, pagination }) {
    // Component implementation
}
```

### 3. Error Handling

```jsx
import { usePage } from '@inertiajs/react'

export default function FormComponent() {
  const { errors } = usePage().props

  // Handle validation errors
  const getError = field => errors[field]?.[0]

  return (
    <form>
      <input name="email" />
      {getError('email') && (
        <span className="text-red-500">{getError('email')}</span>
      )}
    </form>
  )
}
```

### 4. SEO and Meta Tags

```jsx
import { Head } from '@inertiajs/react'

export default function AgentProfile({ agent }) {
  return (
    <>
      <Head>
        <title>
          {agent.name}
          {' '}
          - Agent Profile
        </title>
        <meta name="description" content={agent.bio} />
        <meta property="og:title" content={agent.name} />
        <meta property="og:image" content={agent.avatar_url} />
      </Head>

      <AppLayout title={agent.name}>
        {/* Component content */}
      </AppLayout>
    </>
  )
}
```

## Common Gotchas and Solutions

### 1. Stale Closure in Event Handlers

```jsx
// Problem: Stale closure
const [count, setCount] = useState(0)

useEffect(() => {
  router.on('navigate', () => {
    console.log(count) // Always logs 0
  })
}, [])

// Solution: Use ref or dependency array
useEffect(() => {
  router.on('navigate', () => {
    console.log(count) // Logs current count
  })
}, [count])
```

### 2. Form Data Not Updating

```jsx
// Problem: Not using setData correctly
const { data, setData } = useForm({ name: '' })

// Wrong
data.name = 'New name'

// Correct
setData('name', 'New name')
// Or
setData(prevData => ({ ...prevData, name: 'New name' }))
```

### 3. Missing CSRF Token

```jsx
// Inertia.js automatically includes CSRF tokens
// But for manual requests, ensure you have:

// In your app.jsx
import axios from 'axios'
axios.defaults.headers.common['X-CSRF-TOKEN'] = document.querySelector('meta[name="csrf-token"]').getAttribute('content')
```

This guide covers the essential concepts and patterns for working with Inertia.js and React in the Larasonic project. The combination provides a powerful, productive development experience that leverages the best of both Laravel and React.
