<?php

namespace Tests\Feature;

use App\Models\MediaFolder;
use App\Models\User;
use App\Models\Team;
use App\Enums\UserType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MediaFolderUniquenessTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $team;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a team
        $this->team = Team::factory()->create([
            'name' => 'Test Company',
        ]);

        // Create a user
        $this->user = User::factory()->create([
            'user_type' => UserType::PLATFORM_ADMINISTRATOR,
            'current_team_id' => $this->team->id,
            'profile_image_id' => null, // Add this field to prevent the error
        ]);

        // Attach user to team
        $this->user->teams()->attach($this->team);
    }

    /** @test */
    public function it_creates_unique_folder_names_when_duplicates_exist()
    {
        // Create first folder
        MediaFolder::create([
            'name' => 'Test Folder',
            'slug' => 'test-folder',
            'created_by' => $this->user->id,
            'company_id' => $this->team->id,
        ]);

        $this->assertDatabaseHas('media_folders', [
            'name' => 'Test Folder',
            'slug' => 'test-folder',
        ]);

        // Try to create another folder with same name via API
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => 'Test Folder',
                'description' => 'Second folder with same name',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Check that the second folder has a unique name
        $this->assertDatabaseHas('media_folders', [
            'name' => 'Test Folder (1)',
            'slug' => 'test-folder-1',
        ]);

        // Try to create a third folder with same original name
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => 'Test Folder',
                'description' => 'Third folder with same name',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Check that the third folder has a unique name
        $this->assertDatabaseHas('media_folders', [
            'name' => 'Test Folder (2)',
            'slug' => 'test-folder-2',
        ]);
    }

    /** @test */
    public function it_handles_folder_names_with_special_characters()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => 'My-Folder_2023 (Documents)',
                'description' => 'Folder with special characters',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('media_folders', [
            'name' => 'My-Folder_2023 (Documents)',
            'slug' => 'my-folder-2023-documents',
        ]);
    }

    /** @test */
    public function it_rejects_invalid_folder_names()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => 'Invalid/Folder\\Name*',
                'description' => 'Folder with invalid characters',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    /** @test */
    public function it_cleans_folder_names_properly()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => '   Extra   Spaces   Folder   ',
                'description' => 'Folder with extra spaces',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('media_folders', [
            'name' => 'Extra Spaces Folder',
            'slug' => 'extra-spaces-folder',
        ]);
    }

    /** @test */
    public function it_handles_empty_folder_names()
    {
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => '   ',
                'description' => 'Empty folder name',
            ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    /** @test */
    public function it_creates_unique_names_in_different_parent_folders()
    {
        // Create parent folder
        $parentFolder = MediaFolder::create([
            'name' => 'Parent Folder',
            'slug' => 'parent-folder',
            'created_by' => $this->user->id,
            'company_id' => $this->team->id,
        ]);

        // Create folder in root
        MediaFolder::create([
            'name' => 'Documents',
            'slug' => 'documents',
            'created_by' => $this->user->id,
            'company_id' => $this->team->id,
        ]);

        // Create folder with same name in parent folder (should be allowed)
        $response = $this->actingAs($this->user)
            ->postJson(route('admin.media-folders.store'), [
                'name' => 'Documents',
                'parent_id' => $parentFolder->id,
                'description' => 'Documents in parent folder',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Should have two folders named "Documents" in different locations
        $this->assertEquals(2, MediaFolder::where('name', 'Documents')->count());
    }

    /** @test */
    public function it_updates_folder_names_with_uniqueness_check()
    {
        // Create two folders
        MediaFolder::create([
            'name' => 'Original Folder',
            'slug' => 'original-folder',
            'created_by' => $this->user->id,
            'company_id' => $this->team->id,
        ]);

        $folder2 = MediaFolder::create([
            'name' => 'Another Folder',
            'slug' => 'another-folder',
            'created_by' => $this->user->id,
            'company_id' => $this->team->id,
        ]);

        // Try to rename folder2 to same name as folder1
        $response = $this->actingAs($this->user)
            ->putJson(route('admin.media-folders.update', $folder2), [
                'name' => 'Original Folder',
                'description' => 'Updated description',
            ]);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Check that folder2 now has a unique name
        $folder2->refresh();
        $this->assertEquals('Original Folder (1)', $folder2->name);
        $this->assertEquals('original-folder-1', $folder2->slug);
    }
}
