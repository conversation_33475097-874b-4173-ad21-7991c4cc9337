import React, { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import axios from 'axios';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/Components/shadcn/ui/dialog';
import { Button } from '@/Components/shadcn/ui/button';
import { Input } from '@/Components/shadcn/ui/input';
import { Label } from '@/Components/shadcn/ui/label';
import { Textarea } from '@/Components/shadcn/ui/textarea';
import { Checkbox } from '@/Components/shadcn/ui/checkbox';
import { Folder, Loader2 } from 'lucide-react';
import { useToast } from '@/Components/hooks/use-toast';

export default function EditFolderModal({
    isOpen,
    onClose,
    folder = null,
    onFolderUpdated
}) {
    const [formData, setFormData] = useState({
        name: '',
        description: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [errors, setErrors] = useState({});
    const { toast } = useToast();

    // Update form data when folder changes
    useEffect(() => {
        if (folder) {
            setFormData({
                name: folder.name || '',
                description: folder.description || ''
            });
        }
    }, [folder]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!folder) return;

        setIsSubmitting(true);
        setErrors({});

        try {
            const isCompanyContext = window.location.pathname.includes('/company/');
            const routeName = isCompanyContext ? 'company.media-folders.update' : 'admin.media-folders.update';

            const response = await axios.put(route(routeName, folder.id), formData);

            if (response.data.success) {
                toast({
                    title: "Success",
                    description: response.data.message,
                });

                onClose();

                // Refresh the page to show updated folder
                if (onFolderUpdated) {
                    onFolderUpdated(response.data.folder);
                } else {
                    router.reload();
                }
            }
        } catch (error) {
            if (error.response?.status === 422) {
                setErrors(error.response.data.errors || {});
            } else {
                toast({
                    title: "Error",
                    description: error.response?.data?.message || "Failed to update folder",
                    variant: "destructive",
                });
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        if (!isSubmitting) {
            setErrors({});
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Folder className="w-5 h-5" />
                        Edit Folder
                    </DialogTitle>
                    <DialogDescription>
                        Update the folder details.
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Folder Name */}
                    <div className="space-y-2">
                        <Label htmlFor="folder-name">Folder Name *</Label>
                        <Input
                            id="folder-name"
                            value={formData.name}
                            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                            placeholder="Enter folder name"
                            disabled={isSubmitting}
                            className={errors.name ? 'border-destructive' : ''}
                        />
                        {errors.name && (
                            <p className="text-sm text-destructive">{errors.name[0]}</p>
                        )}
                        <p className="text-xs text-muted-foreground">
                            If a folder with the same name exists, a number will be added automatically
                        </p>
                    </div>

                    {/* Description */}
                    <div className="space-y-2">
                        <Label htmlFor="folder-description">Description</Label>
                        <Textarea
                            id="folder-description"
                            value={formData.description}
                            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="Optional description for this folder"
                            disabled={isSubmitting}
                            rows={3}
                            className={errors.description ? 'border-destructive' : ''}
                        />
                        {errors.description && (
                            <p className="text-sm text-destructive">{errors.description[0]}</p>
                        )}
                    </div>



                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleClose}
                            disabled={isSubmitting}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting || !formData.name.trim()}
                        >
                            {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                            Update Folder
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
