"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rash<PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { Checkbox } from "@/Components/shadcn/ui/checkbox"
import { But<PERSON> } from "@/Components/shadcn/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/Components/shadcn/ui/dropdown-menu"
import { DataTableColumnHeader } from "@/Components/shadcn/ui/data-table-column-header"
import { Badge } from "@/Components/shadcn/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/shadcn/ui/avatar"
import { Link, router } from '@inertiajs/react'
import { route } from 'ziggy-js'
import { toast } from 'sonner'

export const columns = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="User" />
    ),
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.profile_photo_url} alt={user.name} />
            <AvatarFallback>
              {user.name?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{user.name}</div>
            <div className="text-sm text-muted-foreground">{user.email}</div>
          </div>
        </div>
      )
    },
  },
  {
    accessorKey: "user_type",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="User Type" />
    ),
    cell: ({ row }) => {
      const userType = row.getValue("user_type")
      const getVariant = (type) => {
        switch (type?.toLowerCase()) {
          case 'platform_administrator':
            return 'destructive'
          case 'brokerage_admin':
            return 'default'
          case 'independent_agent':
            return 'secondary'
          case 'company_agent':
            return 'outline'
          default:
            return 'outline'
        }
      }

      return (
        <Badge variant={getVariant(userType)}>
          {userType?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown'}
        </Badge>
      )
    },
  },
  {
    accessorKey: "email_verified_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Verified" />
    ),
    cell: ({ row }) => {
      const verified = row.getValue("email_verified_at")
      return (
        <Badge variant={verified ? "default" : "secondary"}>
          {verified ? "Verified" : "Unverified"}
        </Badge>
      )
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"))
      return (
        <div className="text-sm">
          {date.toLocaleDateString()}
        </div>
      )
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const user = row.original

      const handleDelete = () => {
        if (confirm(`Are you sure you want to delete the user "${user.name}"?`)) {
          router.delete(route('admin.users.destroy', user.id), {
            onSuccess: () => {
              toast.success('User deleted successfully')
              window.location.reload() // Refresh the page to update data
            },
            onError: () => {
              toast.error('Failed to delete user')
            }
          })
        }
      }

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(user.id)}
            >
              Copy user ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link href={route('admin.users.show', user.id)}>
                <Eye className="mr-2 h-4 w-4" />
                View user
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href={route('admin.users.edit', user.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit user
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-destructive"
              onClick={handleDelete}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete user
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
