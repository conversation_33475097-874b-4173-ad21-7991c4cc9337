<?php

declare(strict_types=1);

use Laravel\Fortify\Fortify;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create users table with all fields consolidated
        Schema::create('users', function (Blueprint $table): void {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();

            // User type with final enum values
            $table->enum('user_type', ['platform_administrator', 'brokerage_admin', 'company_agent', 'independent_agent'])
                ->default('independent_agent');

            $table->string('password');

            // Two-factor authentication fields
            $table->text('two_factor_secret')->nullable();
            $table->text('two_factor_recovery_codes')->nullable();
            if (Fortify::confirmsTwoFactorAuthentication()) {
                $table->timestamp('two_factor_confirmed_at')->nullable();
            }

            $table->rememberToken();
            $table->foreignId('current_team_id')->nullable();
            $table->string('profile_photo_path', 2048)->nullable();

            // Real estate specific profile fields
            $table->string('license_number')->nullable();
            $table->string('license_state')->nullable();
            $table->date('license_expiry')->nullable();
            $table->string('phone')->nullable();
            $table->text('bio')->nullable();
            $table->json('specialties')->nullable(); // Changed to JSON for better structure

            // Company/Agency specific fields (for brokerage_admin users)
            $table->string('company_name')->nullable();
            $table->string('company_license')->nullable();
            $table->text('company_address')->nullable();
            $table->string('company_website')->nullable();

            // Subscription and team relationships
            $table->boolean('inherits_team_subscription')->default(false);
            $table->unsignedBigInteger('parent_company_id')->nullable(); // Foreign key will be added later

            // Onboarding and profile completion
            $table->boolean('profile_completed')->default(false);
            $table->timestamp('onboarding_completed_at')->nullable();

            // Media management permissions
            $table->boolean('can_access_company_media')->default(false);
            $table->boolean('can_upload_media')->default(true);
            $table->unsignedBigInteger('profile_image_id')->nullable(); // Foreign key will be added later

            // Cashier (Stripe) fields
            $table->string('stripe_id')->nullable()->index();
            $table->string('pm_type')->nullable();
            $table->string('pm_last_four', 4)->nullable();
            $table->timestamp('trial_ends_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index('user_type');
            $table->index('parent_company_id');
            $table->index('profile_image_id');
        });

        // Create teams table with all fields consolidated
        Schema::create('teams', function (Blueprint $table): void {
            $table->id();
            $table->foreignId('user_id')->index();
            $table->string('name');
            $table->boolean('personal_team');

            // Team type for real estate organizations
            $table->enum('team_type', ['agency', 'brokerage', 'team'])->default('agency');

            // Agency/Brokerage specific information
            $table->string('license_number')->nullable();
            $table->string('license_state')->nullable();
            $table->text('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->text('description')->nullable();

            // Business settings
            $table->json('business_hours')->nullable();
            $table->json('service_areas')->nullable(); // Geographic areas served
            $table->json('specialties')->nullable(); // Property types, services

            // Subscription settings for team
            $table->boolean('has_team_subscription')->default(false);
            $table->integer('max_agents')->nullable(); // Based on subscription plan

            $table->timestamps();
        });

        // Create password reset tokens table
        Schema::create('password_reset_tokens', function (Blueprint $table): void {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        // Create sessions table
        Schema::create('sessions', function (Blueprint $table): void {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('teams');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
